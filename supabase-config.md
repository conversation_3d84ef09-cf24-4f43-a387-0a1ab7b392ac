# Supabase Configuration Documentation
# توثيق إعدادات Supabase

## Project Information / معلومات المشروع

This document contains the Supabase project configuration details for the application.
يحتوي هذا المستند على تفاصيل إعدادات مشروع Supabase للتطبيق.

---

## Configuration Details / تفاصيل الإعدادات

### Project URL / رابط المشروع
```
https://bbigwqwtmhctqrptkuni.supabase.co
```
**Description / الوصف:** This is the main URL endpoint for your Supabase project. Use this URL to connect your application to the Supabase backend services.
هذا هو الرابط الرئيسي لمشروع Supabase الخاص بك. استخدم هذا الرابط لربط تطبيقك بخدمات Supabase الخلفية.

---

### Service Role Key / مفتاح دور الخدمة
```
FuXuLhX8XH6sP+nZ4wnZ1XFepEO38LZJ5ExnXpA+0PtJADdfnUhcSZXNGDqqTo6iCD+pBNcrzwvJszcWOTy3XA==
```
**⚠️ IMPORTANT / مهم:** This is the service role key with full administrative access to your database.
هذا هو مفتاح دور الخدمة مع صلاحية إدارية كاملة لقاعدة البيانات الخاصة بك.

**Usage / الاستخدام:** 
- Use ONLY on the server-side / استخدم فقط في الخادم
- Never expose in client-side code / لا تعرضه أبداً في كود العميل
- Has bypass RLS (Row Level Security) permissions / له صلاحيات تجاوز أمان مستوى الصف

---

### Anonymous Public Key / المفتاح العام المجهول
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM
```
**Description / الوصف:** This is the anonymous/public key for client-side usage.
هذا هو المفتاح المجهول/العام للاستخدام في جانب العميل.

**Usage / الاستخدام:**
- Safe to use in client-side applications / آمن للاستخدام في تطبيقات العميل
- Used for public API access / يُستخدم للوصول العام لـ API
- Respects Row Level Security (RLS) policies / يحترم سياسات أمان مستوى الصف

**Token Details / تفاصيل الرمز:**
- Algorithm: HS256 / الخوارزمية
- Issued at: 1746298726 (Unix timestamp) / تاريخ الإصدار
- Expires: 2061874726 (Unix timestamp) / تاريخ الانتهاء

---

### Service Role JWT Token / رمز JWT لدور الخدمة
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjI5ODcyNiwiZXhwIjoyMDYxODc0NzI2fQ.CWaGhsn91tSxfRRiJi1IGiDsvMHmZdX7ril1Q8AOcHA
```
**⚠️ CRITICAL / حرج:** This is the service role JWT token with administrative privileges.
هذا هو رمز JWT لدور الخدمة مع امتيازات إدارية.

**Usage / الاستخدام:**
- Server-side operations only / عمليات الخادم فقط
- Database administration / إدارة قاعدة البيانات
- Bypasses all RLS policies / يتجاوز جميع سياسات RLS

**Token Details / تفاصيل الرمز:**
- Algorithm: HS256 / الخوارزمية
- Role: service_role / الدور
- Issued at: 1746298726 (Unix timestamp) / تاريخ الإصدار
- Expires: 2061874726 (Unix timestamp) / تاريخ الانتهاء

---

## Environment Variables Setup / إعداد متغيرات البيئة

Create a `.env` file in your project root with the following variables:
أنشئ ملف `.env` في جذر مشروعك مع المتغيرات التالية:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://bbigwqwtmhctqrptkuni.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjI5ODcyNiwiZXhwIjoyMDYxODc0NzI2fQ.CWaGhsn91tSxfRRiJi1IGiDsvMHmZdX7ril1Q8AOcHA
```

---

## Security Best Practices / أفضل ممارسات الأمان

### 🔒 Key Security Guidelines / إرشادات الأمان الرئيسية

1. **Never commit sensitive keys to version control**
   لا تضع المفاتيح الحساسة في نظام التحكم بالإصدارات

2. **Use environment variables for configuration**
   استخدم متغيرات البيئة للإعدادات

3. **Implement Row Level Security (RLS) policies**
   طبق سياسات أمان مستوى الصف

4. **Regularly rotate your service keys**
   قم بتدوير مفاتيح الخدمة بانتظام

5. **Monitor API usage and access logs**
   راقب استخدام API وسجلات الوصول

---

## Quick Start / البداية السريعة

### Installation / التثبيت
```bash
npm install @supabase/supabase-js
```

### Basic Setup / الإعداد الأساسي
```javascript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseKey)
```

---

## Support / الدعم

For technical support and documentation, visit:
للدعم الفني والتوثيق، قم بزيارة:
- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Community](https://github.com/supabase/supabase/discussions)

---

**Last Updated / آخر تحديث:** January 2025
**Project Reference / مرجع المشروع:** bbigwqwtmhctqrptkuni
