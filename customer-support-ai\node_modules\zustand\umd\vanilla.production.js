!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).zustandVanilla={})}(this,(function(e){"use strict";var t=function(e){var t,n=new Set,o=function(e,o){var u="function"==typeof e?e(t):e;if(!Object.is(u,t)){var r=t;t=(null!=o?o:"object"!=typeof u||null===u)?u:Object.assign({},t,u),n.forEach((function(e){return e(t,r)}))}},u=function(){return t},r={setState:o,getState:u,getInitialState:function(){return i},subscribe:function(e){return n.add(e),function(){return n.delete(e)}},destroy:function(){n.clear()}},i=t=e(o,u,r);return r},n=function(e){return e?t(e):t};e.createStore=n,e.default=function(e){return n(e)},Object.defineProperty(e,"__esModule",{value:!0})}));
