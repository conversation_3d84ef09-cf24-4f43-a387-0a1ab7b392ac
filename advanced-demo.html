<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام تحليل مشاكل خدمة العملاء - النسخة المتقدمة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .loading {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto p-6 max-w-6xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">
                نظام تحليل مشاكل خدمة العملاء
            </h1>
            <p class="text-lg text-gray-600">
                أدخل وصف المشكلة باللغة العربية وسيتم تحليلها وتصنيفها تلقائياً باستخدام الذكاء الاصطناعي
            </p>
            <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p class="text-sm text-yellow-800">
                    ⚠️ هذه نسخة تجريبية. لاستخدام OpenAI API الحقيقي، يرجى إدخال مفتاح API الخاص بك أدناه.
                </p>
            </div>
        </div>

        <!-- API Key Section -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <label for="api-key" class="block text-lg font-medium text-gray-700 mb-4">
                مفتاح OpenAI API (اختياري للاختبار)
            </label>
            <input
                type="password"
                id="api-key"
                placeholder="sk-..."
                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p class="text-sm text-gray-500 mt-2">
                إذا لم تدخل مفتاح API، سيتم استخدام بيانات تجريبية للعرض
            </p>
        </div>

        <!-- Input Section -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <label for="issue-input" class="block text-lg font-medium text-gray-700 mb-4">
                وصف المشكلة (باللغة العربية)
            </label>
            <textarea
                id="issue-input"
                placeholder="مثال: العميل بيشتكي إن الحساب بتاعه اتمسح ومش قادر يرجع له تاني وبيقول كل البيانات اختفت..."
                class="w-full h-32 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                dir="rtl"
            ></textarea>
            
            <div class="flex justify-between items-center mt-4">
                <div class="text-sm text-gray-500">
                    عدد الأحرف: <span id="char-count">0</span>
                </div>
                <button
                    id="analyze-btn"
                    class="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                    <span id="btn-text">تحليل المشكلة</span>
                    <div id="loading-spinner" class="hidden w-5 h-5 border-2 border-white border-t-transparent rounded-full loading"></div>
                </button>
            </div>
        </div>

        <!-- Results Section -->
        <div id="results-section" class="hidden space-y-6">
            <!-- Success Message -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <span class="text-green-700">
                    تم تحليل المشكلة بنجاح! مستوى الثقة: <span id="confidence-score">0</span>%
                </span>
            </div>

            <!-- Translation -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 class="font-medium text-blue-900 mb-2">الترجمة الإنجليزية:</h3>
                <p id="english-translation" class="text-blue-800"></p>
            </div>

            <!-- Results Table -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-900">نتائج التحليل</h2>
                    <div class="flex gap-2">
                        <button
                            id="copy-btn"
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            نسخ الجدول
                        </button>
                        <button
                            id="save-btn"
                            class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                        >
                            حفظ كـ JSON
                        </button>
                    </div>
                </div>

                <div class="overflow-x-auto border border-gray-300 rounded-lg">
                    <table class="w-full border-collapse bg-white">
                        <thead>
                            <tr class="bg-gradient-to-r from-blue-600 to-blue-700 text-white">
                                <th class="px-6 py-4 text-left text-sm font-bold border-r border-blue-500 min-w-[120px]">
                                    <div class="flex items-center gap-2">
                                        🏢 Department
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r border-blue-500 min-w-[140px]">
                                    <div class="flex items-center gap-2">
                                        📋 Case Type
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r border-blue-500 min-w-[120px]">
                                    <div class="flex items-center gap-2">
                                        📂 Category
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r border-blue-500 min-w-[140px]">
                                    <div class="flex items-center gap-2">
                                        📁 Sub Category
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r border-blue-500 min-w-[160px]">
                                    <div class="flex items-center gap-2">
                                        🗂️ Sub Sub Category
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r border-blue-500 min-w-[200px]">
                                    <div class="flex items-center gap-2">
                                        📝 Description
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r border-blue-500 min-w-[100px]">
                                    <div class="flex items-center gap-2">
                                        ⏰ SLA
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold min-w-[150px]">
                                    <div class="flex items-center gap-2">
                                        📌 Notes
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="results-table-body" class="divide-y divide-gray-200">
                            <!-- Results will be inserted here -->
                        </tbody>
                    </table>
                </div>

                <!-- Markdown Preview -->
                <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
                    <h3 class="text-sm font-medium text-gray-700 mb-2">معاينة الجدول (Markdown):</h3>
                    <div class="bg-white p-4 rounded border text-sm font-mono text-gray-600 overflow-x-auto">
                        <pre id="markdown-preview"></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Section -->
        <div id="error-section" class="hidden bg-red-50 border border-red-200 rounded-lg p-4">
            <span class="text-red-700" id="error-message"></span>
        </div>

        <!-- Examples Section -->
        <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">أمثلة للاختبار:</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button class="example-btn text-right p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors" 
                        data-text="العميل بيشتكي إن الحساب بتاعه اتمسح ومش قادر يرجع له تاني وبيقول كل البيانات اختفت">
                    مشكلة حذف حساب
                </button>
                <button class="example-btn text-right p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                        data-text="الموظف مش قادر يدخل على النظام وبيقول كلمة المرور مش شغالة">
                    مشكلة تسجيل دخول
                </button>
                <button class="example-btn text-right p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                        data-text="العميل دفع الفاتورة بس المبلغ مش ظاهر في النظام">
                    مشكلة دفع
                </button>
                <button class="example-btn text-right p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                        data-text="الطلب متأخر عن الموعد المحدد والعميل زعلان">
                    مشكلة شحن
                </button>
            </div>
        </div>
    </div>

    <script>
        // DOM Elements
        const apiKeyInput = document.getElementById('api-key');
        const issueInput = document.getElementById('issue-input');
        const charCount = document.getElementById('char-count');
        const analyzeBtn = document.getElementById('analyze-btn');
        const btnText = document.getElementById('btn-text');
        const loadingSpinner = document.getElementById('loading-spinner');
        const resultsSection = document.getElementById('results-section');
        const errorSection = document.getElementById('error-section');
        const errorMessage = document.getElementById('error-message');
        const confidenceScore = document.getElementById('confidence-score');
        const englishTranslation = document.getElementById('english-translation');
        const resultsTableBody = document.getElementById('results-table-body');
        const markdownPreview = document.getElementById('markdown-preview');
        const copyBtn = document.getElementById('copy-btn');
        const saveBtn = document.getElementById('save-btn');
        const exampleBtns = document.querySelectorAll('.example-btn');

        let currentResult = null;

        // Character count
        issueInput.addEventListener('input', function() {
            charCount.textContent = this.value.length;
        });

        // Example buttons
        exampleBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                issueInput.value = this.dataset.text;
                charCount.textContent = this.dataset.text.length;
            });
        });

        // Analyze button click
        analyzeBtn.addEventListener('click', async function() {
            const arabicText = issueInput.value.trim();
            const apiKey = apiKeyInput.value.trim();
            
            if (!arabicText) {
                showError('يرجى إدخال وصف المشكلة');
                return;
            }

            setLoading(true);
            hideError();
            hideResults();

            try {
                let result;
                
                if (apiKey) {
                    // Use real OpenAI API
                    result = await analyzeWithOpenAI(arabicText, apiKey);
                } else {
                    // Use mock data
                    result = await getMockAnalysis(arabicText);
                }

                currentResult = result;
                displayResults(result);
            } catch (error) {
                showError('حدث خطأ أثناء تحليل المشكلة: ' + error.message);
            } finally {
                setLoading(false);
            }
        });

        // Copy button click
        copyBtn.addEventListener('click', function() {
            const markdownText = markdownPreview.textContent;
            navigator.clipboard.writeText(markdownText).then(() => {
                copyBtn.textContent = 'تم النسخ!';
                setTimeout(() => {
                    copyBtn.textContent = 'نسخ الجدول';
                }, 2000);
            });
        });

        // Save button click
        saveBtn.addEventListener('click', function() {
            if (currentResult) {
                const dataStr = JSON.stringify(currentResult, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = 'analysis-result.json';
                link.click();
                URL.revokeObjectURL(url);
            }
        });

        async function analyzeWithOpenAI(arabicText, apiKey) {
            const response = await fetch('https://api.openai.com/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                    model: 'gpt-4',
                    messages: [
                        {
                            role: 'system',
                            content: `أنت خبير دعم فني ومساعد ذكي لفريق خدمة العملاء.

وظيفتك تحليل أي مشكلة يتم شرحها باللغة العربية من أحد الموظفين أو العملاء، وتصنيفها بدقة إلى العناصر التالية:

Department | Case Type | Category | Sub Category | Sub Sub Category | Description | SLA | Notes

يرجى اتباع التعليمات التالية بدقة:
- افهم المشكلة جيدًا حتى لو كانت غير مرتبة
- ترجم المفاهيم من اللغة العربية إلى اللغة الإنجليزية
- إذا لم تُذكر بعض التفاصيل بوضوح، استنتجها من السياق أو اترك الخانة "N/A"
- اجعل عمود "SLA" يحتوي على الوقت المتوقع للحل (مثل: 2 hours, 24 hours, 3 days)
- اجعل الوصف مختصرًا وواضحًا باللغة الإنجليزية في عمود Description
- قدم أيضاً ترجمة كاملة للنص العربي إلى الإنجليزية
- قيم مستوى الثقة في التحليل من 0 إلى 100

أمثلة على التصنيفات الشائعة:
- Department: IT, Finance, HR, Logistics, Customer Service, Sales
- Case Type: Technical Issue, Account Problem, Payment Issue, Shipping Problem, General Inquiry
- Category: Hardware, Software, Network, Database, etc.

يجب أن تعيد النتيجة في تنسيق JSON فقط مع الحقول التالية:
{
  "department": "",
  "case_type": "",
  "category": "",
  "sub_category": "",
  "sub_sub_category": "",
  "description": "",
  "sla": "",
  "notes": "",
  "english_translation": "",
  "confidence_score": 0
}`
                        },
                        {
                            role: 'user',
                            content: `قم بتحليل المشكلة التالية: "${arabicText}"`
                        }
                    ],
                    temperature: 0.3,
                    max_tokens: 1000
                })
            });

            if (!response.ok) {
                throw new Error('فشل في الاتصال بـ OpenAI API');
            }

            const data = await response.json();
            const responseText = data.choices[0]?.message?.content;
            
            if (!responseText) {
                throw new Error('لم يتم الحصول على رد من OpenAI');
            }

            // Parse JSON response
            const jsonMatch = responseText.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('رد غير صالح من OpenAI');
            }

            return JSON.parse(jsonMatch[0]);
        }

        async function getMockAnalysis(arabicText) {
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Simple keyword-based mock analysis
            const text = arabicText.toLowerCase();
            
            if (text.includes('حساب') || text.includes('اتمسح') || text.includes('دخول')) {
                return {
                    department: 'IT',
                    case_type: 'Account Problem',
                    category: 'Access',
                    sub_category: 'Account Deleted',
                    sub_sub_category: 'Data Loss',
                    description: 'Customer reports account deletion with complete data loss',
                    sla: '4 hours',
                    notes: 'Urgent - requires immediate attention',
                    english_translation: 'The customer is complaining that their account has been deleted and they cannot access it again, saying all data has disappeared',
                    confidence_score: 85
                };
            } else if (text.includes('دفع') || text.includes('فاتورة') || text.includes('مبلغ')) {
                return {
                    department: 'Finance',
                    case_type: 'Payment Issue',
                    category: 'Transaction',
                    sub_category: 'Payment Not Reflected',
                    sub_sub_category: 'System Sync Issue',
                    description: 'Customer paid but amount not showing in system',
                    sla: '24 hours',
                    notes: 'Check with payment gateway',
                    english_translation: 'The customer paid the invoice but the amount is not showing in the system',
                    confidence_score: 90
                };
            } else if (text.includes('طلب') || text.includes('شحن') || text.includes('متأخر')) {
                return {
                    department: 'Logistics',
                    case_type: 'Shipping Problem',
                    category: 'Delay',
                    sub_category: 'Late Delivery',
                    sub_sub_category: 'Customer Complaint',
                    description: 'Order delayed beyond scheduled delivery date',
                    sla: '48 hours',
                    notes: 'Contact shipping provider',
                    english_translation: 'The order is delayed beyond the scheduled date and the customer is upset',
                    confidence_score: 88
                };
            } else {
                return {
                    department: 'Customer Service',
                    case_type: 'General Inquiry',
                    category: 'Support',
                    sub_category: 'General Question',
                    sub_sub_category: 'N/A',
                    description: 'General customer inquiry requiring support',
                    sla: '12 hours',
                    notes: 'Standard support process',
                    english_translation: arabicText,
                    confidence_score: 70
                };
            }
        }

        function setLoading(loading) {
            if (loading) {
                btnText.textContent = 'جاري التحليل...';
                loadingSpinner.classList.remove('hidden');
                analyzeBtn.disabled = true;
            } else {
                btnText.textContent = 'تحليل المشكلة';
                loadingSpinner.classList.add('hidden');
                analyzeBtn.disabled = false;
            }
        }

        function showError(message) {
            errorMessage.textContent = message;
            errorSection.classList.remove('hidden');
        }

        function hideError() {
            errorSection.classList.add('hidden');
        }

        function hideResults() {
            resultsSection.classList.add('hidden');
        }

        function displayResults(result) {
            // Update confidence score and translation
            confidenceScore.textContent = result.confidence_score;
            englishTranslation.textContent = result.english_translation;

            // Get color schemes for different elements
            const departmentColors = {
                'IT': 'bg-blue-100 text-blue-800 border-blue-300',
                'Finance': 'bg-green-100 text-green-800 border-green-300',
                'HR': 'bg-purple-100 text-purple-800 border-purple-300',
                'Logistics': 'bg-orange-100 text-orange-800 border-orange-300',
                'Customer Service': 'bg-pink-100 text-pink-800 border-pink-300',
                'Sales': 'bg-indigo-100 text-indigo-800 border-indigo-300'
            };

            const slaColors = {
                '1 hour': 'bg-red-100 text-red-800 border-red-300',
                '2 hours': 'bg-orange-100 text-orange-800 border-orange-300',
                '4 hours': 'bg-yellow-100 text-yellow-800 border-yellow-300',
                '12 hours': 'bg-blue-100 text-blue-800 border-blue-300',
                '24 hours': 'bg-green-100 text-green-800 border-green-300',
                '48 hours': 'bg-gray-100 text-gray-800 border-gray-300'
            };

            const deptColor = departmentColors[result.department] || 'bg-gray-100 text-gray-800 border-gray-300';
            const slaColor = slaColors[result.sla] || 'bg-gray-100 text-gray-800 border-gray-300';

            // Create enhanced table row with beautiful styling
            const row = `
                <tr class="hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 border-b border-gray-200">
                    <td class="px-6 py-5 border-r border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-3 h-3 rounded-full bg-blue-500"></div>
                            <span class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-bold border-2 ${deptColor} shadow-sm">
                                🏢 ${result.department}
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-5 border-r border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-3 h-3 rounded-full bg-emerald-500"></div>
                            <span class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-bold bg-emerald-100 text-emerald-800 border-2 border-emerald-300 shadow-sm">
                                📋 ${result.case_type}
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-5 border-r border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-3 h-3 rounded-full bg-amber-500"></div>
                            <span class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-bold bg-amber-100 text-amber-800 border-2 border-amber-300 shadow-sm">
                                📂 ${result.category}
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-5 text-sm font-semibold text-gray-900 border-r border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                            <span class="bg-gray-50 px-3 py-2 rounded-md border border-gray-200">${result.sub_category}</span>
                        </div>
                    </td>
                    <td class="px-6 py-5 text-sm font-medium text-gray-700 border-r border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                            <span class="bg-gray-50 px-3 py-2 rounded-md border border-gray-200">${result.sub_sub_category}</span>
                        </div>
                    </td>
                    <td class="px-6 py-5 text-sm text-gray-900 border-r border-gray-200">
                        <div class="bg-white p-3 rounded-lg border border-gray-200 shadow-sm">
                            <p class="text-sm leading-relaxed font-medium">${result.description}</p>
                        </div>
                    </td>
                    <td class="px-6 py-5 border-r border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-3 h-3 rounded-full bg-red-500 animate-pulse"></div>
                            <span class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-bold border-2 ${slaColor} shadow-sm">
                                ⏰ ${result.sla}
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-5 text-sm text-gray-700">
                        <div class="flex items-start gap-3">
                            <span class="text-yellow-500 text-lg">⚠️</span>
                            <div class="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
                                <span class="text-sm font-medium text-yellow-800">${result.notes}</span>
                            </div>
                        </div>
                    </td>
                </tr>
            `;

            resultsTableBody.innerHTML = row;

            // Create markdown preview
            const markdown = `| department | case_type | category | sub_category | sub_sub_category | description | SLA | notes |
| ---------- | --------- | -------- | ------------ | ---------------- | ----------- | --- | ----- |
| ${result.department} | ${result.case_type} | ${result.category} | ${result.sub_category} | ${result.sub_sub_category} | ${result.description} | ${result.sla} | ${result.notes} |`;

            markdownPreview.textContent = markdown;

            // Show results
            resultsSection.classList.remove('hidden');
        }
    </script>
</body>
</html>
