<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام تحليل مشاكل خدمة العملاء - النسخة المتقدمة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .loading {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto p-6 max-w-6xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">
                نظام تحليل مشاكل خدمة العملاء
            </h1>
            <p class="text-lg text-gray-600">
                أدخل وصف المشكلة باللغة العربية وسيتم تحليلها وتصنيفها تلقائياً باستخدام الذكاء الاصطناعي
            </p>
            <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p class="text-sm text-yellow-800">
                    ⚠️ هذه نسخة تجريبية. لاستخدام OpenAI API الحقيقي، يرجى إدخال مفتاح API الخاص بك أدناه.
                </p>
            </div>
        </div>

        <!-- Database Configuration Section -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                🗄️ إعدادات قاعدة البيانات والذكاء الاصطناعي
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="supabase-url" class="block text-sm font-medium text-gray-700 mb-2">
                        Supabase URL
                    </label>
                    <input type="text" id="supabase-url" placeholder="https://your-project.supabase.co"
                           value="https://bbigwqwtmhctqrptkuni.supabase.co"
                           class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <div>
                    <label for="supabase-key" class="block text-sm font-medium text-gray-700 mb-2">
                        Supabase Anon Key
                    </label>
                    <input type="password" id="supabase-key" placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                           class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
            </div>
            <div class="mt-4">
                <label for="api-key" class="block text-sm font-medium text-gray-700 mb-2">
                    مفتاح OpenAI API (اختياري للتحليل الذكي)
                </label>
                <input type="password" id="api-key" placeholder="sk-..."
                       class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p class="text-sm text-blue-800">
                    💡 سيتم حفظ الحالات في قاعدة البيانات وعرض الاقتراحات الذكية من الحالات المشابهة
                </p>
            </div>
        </div>

        <!-- Input Section with Smart Suggestions -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <label for="issue-input" class="block text-lg font-medium text-gray-700 mb-4">
                📝 وصف المشكلة (باللغة العربية)
            </label>
            <div class="relative">
                <textarea
                    id="issue-input"
                    placeholder="مثال: العميل بيشتكي إن الحساب بتاعه اتمسح ومش قادر يرجع له تاني وبيقول كل البيانات اختفت..."
                    class="w-full h-32 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    dir="rtl"
                ></textarea>

                <!-- Smart Suggestions Dropdown -->
                <div id="suggestions-dropdown" class="hidden absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                    <div class="p-3 bg-gray-50 border-b border-gray-200">
                        <h4 class="text-sm font-semibold text-gray-700 flex items-center gap-2">
                            🔍 اقتراحات ذكية من الحالات المشابهة
                        </h4>
                    </div>
                    <div id="suggestions-list">
                        <!-- Suggestions will be inserted here -->
                    </div>
                </div>
            </div>

            <div class="flex justify-between items-center mt-4">
                <div class="text-sm text-gray-500">
                    عدد الأحرف: <span id="char-count">0</span>
                    <span id="suggestions-count" class="hidden ml-4 text-blue-600 font-medium"></span>
                </div>
                <div class="flex gap-2">
                    <button
                        id="search-btn"
                        class="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                        🔍 بحث في الحالات
                    </button>
                    <button
                        id="analyze-btn"
                        class="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                        <span id="btn-text">تحليل المشكلة</span>
                        <div id="loading-spinner" class="hidden w-5 h-5 border-2 border-white border-t-transparent rounded-full loading"></div>
                    </button>
                </div>
            </div>
        </div>

        <!-- Smart Suggestions Section -->
        <div id="suggestions-section" class="hidden bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg shadow-lg p-6 mb-6 border border-green-200">
            <h3 class="text-lg font-bold text-green-800 mb-4 flex items-center gap-2">
                💡 حلول جاهزة مقترحة
            </h3>
            <div id="suggestions-content">
                <!-- Smart suggestions will be inserted here -->
            </div>
        </div>

        <!-- Results Section -->
        <div id="results-section" class="hidden space-y-6">
            <!-- Success Message -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <span class="text-green-700">
                    تم تحليل المشكلة بنجاح! مستوى الثقة: <span id="confidence-score">0</span>%
                </span>
            </div>

            <!-- Translation -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 class="font-medium text-blue-900 mb-2">الترجمة الإنجليزية:</h3>
                <p id="english-translation" class="text-blue-800"></p>
            </div>

            <!-- Results Table -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-900">نتائج التحليل</h2>
                    <div class="flex gap-2">
                        <button
                            id="save-case-btn"
                            class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                        >
                            💾 حفظ كحالة جديدة
                        </button>
                        <button
                            id="copy-btn"
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            📋 نسخ الجدول
                        </button>
                        <button
                            id="save-btn"
                            class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                        >
                            📄 حفظ كـ JSON
                        </button>
                    </div>
                </div>

                <div class="overflow-x-auto border border-gray-300 rounded-lg">
                    <table class="w-full border-collapse bg-white">
                        <thead>
                            <tr class="bg-gradient-to-r from-blue-600 to-blue-700 text-white">
                                <th class="px-6 py-4 text-left text-sm font-bold border-r border-blue-500 min-w-[120px]">
                                    <div class="flex items-center gap-2">
                                        🏢 Department
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r border-blue-500 min-w-[140px]">
                                    <div class="flex items-center gap-2">
                                        📋 Case Type
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r border-blue-500 min-w-[120px]">
                                    <div class="flex items-center gap-2">
                                        📂 Category
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r border-blue-500 min-w-[140px]">
                                    <div class="flex items-center gap-2">
                                        📁 Sub Category
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r border-blue-500 min-w-[160px]">
                                    <div class="flex items-center gap-2">
                                        🗂️ Sub Sub Category
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r border-blue-500 min-w-[200px]">
                                    <div class="flex items-center gap-2">
                                        📝 Description
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r border-blue-500 min-w-[100px]">
                                    <div class="flex items-center gap-2">
                                        ⏰ SLA
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold min-w-[150px]">
                                    <div class="flex items-center gap-2">
                                        📌 Notes
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="results-table-body" class="divide-y divide-gray-200">
                            <!-- Results will be inserted here -->
                        </tbody>
                    </table>
                </div>

                <!-- Markdown Preview -->
                <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
                    <h3 class="text-sm font-medium text-gray-700 mb-2">معاينة الجدول (Markdown):</h3>
                    <div class="bg-white p-4 rounded border text-sm font-mono text-gray-600 overflow-x-auto">
                        <pre id="markdown-preview"></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Section -->
        <div id="error-section" class="hidden bg-red-50 border border-red-200 rounded-lg p-4">
            <span class="text-red-700" id="error-message"></span>
        </div>

        <!-- Examples Section -->
        <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">أمثلة للاختبار:</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button class="example-btn text-right p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors" 
                        data-text="العميل بيشتكي إن الحساب بتاعه اتمسح ومش قادر يرجع له تاني وبيقول كل البيانات اختفت">
                    مشكلة حذف حساب
                </button>
                <button class="example-btn text-right p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                        data-text="الموظف مش قادر يدخل على النظام وبيقول كلمة المرور مش شغالة">
                    مشكلة تسجيل دخول
                </button>
                <button class="example-btn text-right p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                        data-text="العميل دفع الفاتورة بس المبلغ مش ظاهر في النظام">
                    مشكلة دفع
                </button>
                <button class="example-btn text-right p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                        data-text="الطلب متأخر عن الموعد المحدد والعميل زعلان">
                    مشكلة شحن
                </button>
            </div>
        </div>
    </div>

    <script>
        // DOM Elements
        const apiKeyInput = document.getElementById('api-key');
        const supabaseUrlInput = document.getElementById('supabase-url');
        const supabaseKeyInput = document.getElementById('supabase-key');
        const issueInput = document.getElementById('issue-input');
        const charCount = document.getElementById('char-count');
        const analyzeBtn = document.getElementById('analyze-btn');
        const searchBtn = document.getElementById('search-btn');
        const btnText = document.getElementById('btn-text');
        const loadingSpinner = document.getElementById('loading-spinner');
        const resultsSection = document.getElementById('results-section');
        const errorSection = document.getElementById('error-section');
        const errorMessage = document.getElementById('error-message');
        const confidenceScore = document.getElementById('confidence-score');
        const englishTranslation = document.getElementById('english-translation');
        const resultsTableBody = document.getElementById('results-table-body');
        const markdownPreview = document.getElementById('markdown-preview');
        const copyBtn = document.getElementById('copy-btn');
        const saveBtn = document.getElementById('save-btn');
        const saveCaseBtn = document.getElementById('save-case-btn');
        const exampleBtns = document.querySelectorAll('.example-btn');
        const suggestionsDropdown = document.getElementById('suggestions-dropdown');
        const suggestionsList = document.getElementById('suggestions-list');
        const suggestionsSection = document.getElementById('suggestions-section');
        const suggestionsContent = document.getElementById('suggestions-content');
        const suggestionsCount = document.getElementById('suggestions-count');

        let currentResult = null;
        let supabaseClient = null;
        let searchTimeout = null;

        // Initialize Supabase
        function initializeSupabase() {
            const url = supabaseUrlInput.value.trim();
            const key = supabaseKeyInput.value.trim();

            if (url && key) {
                try {
                    supabaseClient = supabase.createClient(url, key);
                    console.log('Supabase initialized successfully');
                    return true;
                } catch (error) {
                    console.error('Failed to initialize Supabase:', error);
                    return false;
                }
            }
            return false;
        }

        // Character count and smart suggestions
        issueInput.addEventListener('input', function() {
            charCount.textContent = this.value.length;

            // Clear previous timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // Show suggestions after user stops typing for 500ms
            if (this.value.length > 10) {
                searchTimeout = setTimeout(() => {
                    showSmartSuggestions(this.value);
                }, 500);
            } else {
                hideSuggestions();
            }
        });

        // Example buttons
        exampleBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                issueInput.value = this.dataset.text;
                charCount.textContent = this.dataset.text.length;
            });
        });

        // Analyze button click
        analyzeBtn.addEventListener('click', async function() {
            const arabicText = issueInput.value.trim();
            const apiKey = apiKeyInput.value.trim();
            
            if (!arabicText) {
                showError('يرجى إدخال وصف المشكلة');
                return;
            }

            setLoading(true);
            hideError();
            hideResults();

            try {
                let result;
                
                if (apiKey) {
                    // Use real OpenAI API
                    result = await analyzeWithOpenAI(arabicText, apiKey);
                } else {
                    // Use mock data
                    result = await getMockAnalysis(arabicText);
                }

                currentResult = result;
                displayResults(result);
            } catch (error) {
                showError('حدث خطأ أثناء تحليل المشكلة: ' + error.message);
            } finally {
                setLoading(false);
            }
        });

        // Copy button click
        copyBtn.addEventListener('click', function() {
            const markdownText = markdownPreview.textContent;
            navigator.clipboard.writeText(markdownText).then(() => {
                copyBtn.textContent = 'تم النسخ!';
                setTimeout(() => {
                    copyBtn.textContent = 'نسخ الجدول';
                }, 2000);
            });
        });

        // Search button click
        searchBtn.addEventListener('click', async function() {
            const searchText = issueInput.value.trim();
            if (!searchText) {
                showError('يرجى إدخال نص للبحث');
                return;
            }

            await searchInDatabase(searchText);
        });

        // Save case button click
        saveCaseBtn.addEventListener('click', async function() {
            if (!currentResult) {
                showError('لا توجد نتائج لحفظها');
                return;
            }

            await saveCaseToDatabase(currentResult);
        });

        // Save button click
        saveBtn.addEventListener('click', function() {
            if (currentResult) {
                const dataStr = JSON.stringify(currentResult, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = 'analysis-result.json';
                link.click();
                URL.revokeObjectURL(url);
            }
        });

        // Smart suggestions functions
        async function showSmartSuggestions(text) {
            if (!initializeSupabase()) {
                // Use mock suggestions if no Supabase
                showMockSuggestions(text);
                return;
            }

            try {
                // Search in solutions library
                const { data: solutions, error } = await supabaseClient
                    .rpc('search_solutions', { search_query: text });

                if (error) throw error;

                if (solutions && solutions.length > 0) {
                    displaySuggestions(solutions);
                } else {
                    showMockSuggestions(text);
                }
            } catch (error) {
                console.error('Error fetching suggestions:', error);
                showMockSuggestions(text);
            }
        }

        function showMockSuggestions(text) {
            const mockSuggestions = [
                {
                    title: 'مشكلة مشابهة: حذف الحساب',
                    description: 'Customer reports account deletion',
                    solution: 'Check admin panel, verify identity, restore from backup',
                    rank: 0.8
                },
                {
                    title: 'حل جاهز: استرداد البيانات',
                    description: 'Data recovery procedure',
                    solution: 'Contact IT team, check backup systems, restore user data',
                    rank: 0.7
                }
            ];

            displaySuggestions(mockSuggestions);
        }

        function displaySuggestions(suggestions) {
            if (!suggestions || suggestions.length === 0) {
                hideSuggestions();
                return;
            }

            suggestionsList.innerHTML = '';

            suggestions.forEach((suggestion, index) => {
                const suggestionElement = document.createElement('div');
                suggestionElement.className = 'p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0';
                suggestionElement.innerHTML = `
                    <div class="flex items-start gap-3">
                        <div class="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                        <div class="flex-1">
                            <h5 class="font-semibold text-gray-800 text-sm">${suggestion.title}</h5>
                            <p class="text-gray-600 text-xs mt-1">${suggestion.description}</p>
                            <div class="mt-2 p-2 bg-green-50 rounded text-xs text-green-800">
                                <strong>الحل:</strong> ${suggestion.solution}
                            </div>
                        </div>
                        <div class="text-xs text-gray-500">
                            ${Math.round((suggestion.rank || 0.5) * 100)}%
                        </div>
                    </div>
                `;

                suggestionElement.addEventListener('click', () => {
                    applySuggestion(suggestion);
                });

                suggestionsList.appendChild(suggestionElement);
            });

            suggestionsDropdown.classList.remove('hidden');
            suggestionsCount.textContent = `${suggestions.length} اقتراح متاح`;
            suggestionsCount.classList.remove('hidden');
        }

        function applySuggestion(suggestion) {
            // Fill the result with suggestion data
            const result = {
                department: 'IT', // Default, can be improved
                case_type: 'Account Problem',
                category: 'Access',
                sub_category: 'Account Recovery',
                sub_sub_category: 'Data Restoration',
                description: suggestion.description,
                solution: suggestion.solution,
                sla: '4 hours',
                notes: 'Applied from smart suggestion',
                english_translation: suggestion.description,
                confidence_score: Math.round((suggestion.rank || 0.5) * 100)
            };

            currentResult = result;
            displayResults(result);
            hideSuggestions();
        }

        function hideSuggestions() {
            suggestionsDropdown.classList.add('hidden');
            suggestionsCount.classList.add('hidden');
        }

        // Database functions
        async function searchInDatabase(searchText) {
            if (!initializeSupabase()) {
                showError('يرجى إدخال بيانات Supabase للبحث في قاعدة البيانات');
                return;
            }

            try {
                const { data: cases, error } = await supabaseClient
                    .rpc('search_cases', { search_query: searchText });

                if (error) throw error;

                if (cases && cases.length > 0) {
                    displaySearchResults(cases);
                } else {
                    showError('لم يتم العثور على حالات مشابهة');
                }
            } catch (error) {
                console.error('Error searching database:', error);
                showError('خطأ في البحث: ' + error.message);
            }
        }

        function displaySearchResults(cases) {
            suggestionsContent.innerHTML = '';

            cases.forEach((caseItem, index) => {
                const caseElement = document.createElement('div');
                caseElement.className = 'bg-white p-4 rounded-lg border border-green-200 mb-3';
                caseElement.innerHTML = `
                    <div class="flex justify-between items-start mb-2">
                        <h4 class="font-semibold text-green-800">${caseItem.department} - ${caseItem.case_type}</h4>
                        <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">${Math.round(caseItem.rank * 100)}% تطابق</span>
                    </div>
                    <p class="text-gray-700 text-sm mb-2">${caseItem.arabic_description}</p>
                    <p class="text-gray-600 text-xs mb-2"><strong>الترجمة:</strong> ${caseItem.english_translation}</p>
                    ${caseItem.solution ? `<div class="bg-green-50 p-2 rounded text-xs text-green-800"><strong>الحل:</strong> ${caseItem.solution}</div>` : ''}
                `;

                suggestionsContent.appendChild(caseElement);
            });

            suggestionsSection.classList.remove('hidden');
        }

        async function saveCaseToDatabase(result) {
            if (!initializeSupabase()) {
                showError('يرجى إدخال بيانات Supabase لحفظ الحالة');
                return;
            }

            try {
                const caseData = {
                    arabic_description: issueInput.value.trim(),
                    english_translation: result.english_translation,
                    department: result.department,
                    case_type: result.case_type,
                    category: result.category,
                    sub_category: result.sub_category,
                    sub_sub_category: result.sub_sub_category,
                    description: result.description,
                    solution: result.solution || null,
                    sla: result.sla,
                    notes: result.notes,
                    confidence_score: result.confidence_score,
                    created_by: 'System User', // Can be improved with actual user
                    status: 'open'
                };

                const { data, error } = await supabaseClient
                    .from('cases')
                    .insert([caseData])
                    .select();

                if (error) throw error;

                // Show success message
                const successDiv = document.createElement('div');
                successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-50';
                successDiv.innerHTML = `
                    <div class="flex items-center gap-2">
                        <span>✅</span>
                        <span>تم حفظ الحالة بنجاح في قاعدة البيانات</span>
                    </div>
                `;
                document.body.appendChild(successDiv);

                setTimeout(() => {
                    document.body.removeChild(successDiv);
                }, 3000);

            } catch (error) {
                console.error('Error saving case:', error);
                showError('خطأ في حفظ الحالة: ' + error.message);
            }
        }

        async function analyzeWithOpenAI(arabicText, apiKey) {
            const response = await fetch('https://api.openai.com/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                    model: 'gpt-4',
                    messages: [
                        {
                            role: 'system',
                            content: `أنت خبير دعم فني ومساعد ذكي لفريق خدمة العملاء.

وظيفتك تحليل أي مشكلة يتم شرحها باللغة العربية من أحد الموظفين أو العملاء، وتصنيفها بدقة إلى العناصر التالية:

Department | Case Type | Category | Sub Category | Sub Sub Category | Description | SLA | Notes

يرجى اتباع التعليمات التالية بدقة:
- افهم المشكلة جيدًا حتى لو كانت غير مرتبة
- ترجم المفاهيم من اللغة العربية إلى اللغة الإنجليزية
- إذا لم تُذكر بعض التفاصيل بوضوح، استنتجها من السياق أو اترك الخانة "N/A"
- اجعل عمود "SLA" يحتوي على الوقت المتوقع للحل (مثل: 2 hours, 24 hours, 3 days)
- اجعل الوصف مختصرًا وواضحًا باللغة الإنجليزية في عمود Description
- قدم أيضاً ترجمة كاملة للنص العربي إلى الإنجليزية
- قيم مستوى الثقة في التحليل من 0 إلى 100

أمثلة على التصنيفات الشائعة:
- Department: IT, Finance, HR, Logistics, Customer Service, Sales
- Case Type: Technical Issue, Account Problem, Payment Issue, Shipping Problem, General Inquiry
- Category: Hardware, Software, Network, Database, etc.

يجب أن تعيد النتيجة في تنسيق JSON فقط مع الحقول التالية:
{
  "department": "",
  "case_type": "",
  "category": "",
  "sub_category": "",
  "sub_sub_category": "",
  "description": "",
  "sla": "",
  "notes": "",
  "english_translation": "",
  "confidence_score": 0
}`
                        },
                        {
                            role: 'user',
                            content: `قم بتحليل المشكلة التالية: "${arabicText}"`
                        }
                    ],
                    temperature: 0.3,
                    max_tokens: 1000
                })
            });

            if (!response.ok) {
                throw new Error('فشل في الاتصال بـ OpenAI API');
            }

            const data = await response.json();
            const responseText = data.choices[0]?.message?.content;
            
            if (!responseText) {
                throw new Error('لم يتم الحصول على رد من OpenAI');
            }

            // Parse JSON response
            const jsonMatch = responseText.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('رد غير صالح من OpenAI');
            }

            return JSON.parse(jsonMatch[0]);
        }

        async function getMockAnalysis(arabicText) {
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Simple keyword-based mock analysis
            const text = arabicText.toLowerCase();
            
            if (text.includes('حساب') || text.includes('اتمسح') || text.includes('دخول')) {
                return {
                    department: 'IT',
                    case_type: 'Account Problem',
                    category: 'Access',
                    sub_category: 'Account Deleted',
                    sub_sub_category: 'Data Loss',
                    description: 'Customer reports account deletion with complete data loss',
                    sla: '4 hours',
                    notes: 'Urgent - requires immediate attention',
                    english_translation: 'The customer is complaining that their account has been deleted and they cannot access it again, saying all data has disappeared',
                    confidence_score: 85
                };
            } else if (text.includes('دفع') || text.includes('فاتورة') || text.includes('مبلغ')) {
                return {
                    department: 'Finance',
                    case_type: 'Payment Issue',
                    category: 'Transaction',
                    sub_category: 'Payment Not Reflected',
                    sub_sub_category: 'System Sync Issue',
                    description: 'Customer paid but amount not showing in system',
                    sla: '24 hours',
                    notes: 'Check with payment gateway',
                    english_translation: 'The customer paid the invoice but the amount is not showing in the system',
                    confidence_score: 90
                };
            } else if (text.includes('طلب') || text.includes('شحن') || text.includes('متأخر')) {
                return {
                    department: 'Logistics',
                    case_type: 'Shipping Problem',
                    category: 'Delay',
                    sub_category: 'Late Delivery',
                    sub_sub_category: 'Customer Complaint',
                    description: 'Order delayed beyond scheduled delivery date',
                    sla: '48 hours',
                    notes: 'Contact shipping provider',
                    english_translation: 'The order is delayed beyond the scheduled date and the customer is upset',
                    confidence_score: 88
                };
            } else {
                return {
                    department: 'Customer Service',
                    case_type: 'General Inquiry',
                    category: 'Support',
                    sub_category: 'General Question',
                    sub_sub_category: 'N/A',
                    description: 'General customer inquiry requiring support',
                    sla: '12 hours',
                    notes: 'Standard support process',
                    english_translation: arabicText,
                    confidence_score: 70
                };
            }
        }

        function setLoading(loading) {
            if (loading) {
                btnText.textContent = 'جاري التحليل...';
                loadingSpinner.classList.remove('hidden');
                analyzeBtn.disabled = true;
            } else {
                btnText.textContent = 'تحليل المشكلة';
                loadingSpinner.classList.add('hidden');
                analyzeBtn.disabled = false;
            }
        }

        function showError(message) {
            errorMessage.textContent = message;
            errorSection.classList.remove('hidden');
        }

        function hideError() {
            errorSection.classList.add('hidden');
        }

        function hideResults() {
            resultsSection.classList.add('hidden');
        }

        function displayResults(result) {
            // Update confidence score and translation
            confidenceScore.textContent = result.confidence_score;
            englishTranslation.textContent = result.english_translation;

            // Get color schemes for different elements
            const departmentColors = {
                'IT': 'bg-blue-100 text-blue-800 border-blue-300',
                'Finance': 'bg-green-100 text-green-800 border-green-300',
                'HR': 'bg-purple-100 text-purple-800 border-purple-300',
                'Logistics': 'bg-orange-100 text-orange-800 border-orange-300',
                'Customer Service': 'bg-pink-100 text-pink-800 border-pink-300',
                'Sales': 'bg-indigo-100 text-indigo-800 border-indigo-300'
            };

            const slaColors = {
                '1 hour': 'bg-red-100 text-red-800 border-red-300',
                '2 hours': 'bg-orange-100 text-orange-800 border-orange-300',
                '4 hours': 'bg-yellow-100 text-yellow-800 border-yellow-300',
                '12 hours': 'bg-blue-100 text-blue-800 border-blue-300',
                '24 hours': 'bg-green-100 text-green-800 border-green-300',
                '48 hours': 'bg-gray-100 text-gray-800 border-gray-300'
            };

            const deptColor = departmentColors[result.department] || 'bg-gray-100 text-gray-800 border-gray-300';
            const slaColor = slaColors[result.sla] || 'bg-gray-100 text-gray-800 border-gray-300';

            // Create enhanced table row with beautiful styling
            const row = `
                <tr class="hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 border-b border-gray-200">
                    <td class="px-6 py-5 border-r border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-3 h-3 rounded-full bg-blue-500"></div>
                            <span class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-bold border-2 ${deptColor} shadow-sm">
                                🏢 ${result.department}
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-5 border-r border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-3 h-3 rounded-full bg-emerald-500"></div>
                            <span class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-bold bg-emerald-100 text-emerald-800 border-2 border-emerald-300 shadow-sm">
                                📋 ${result.case_type}
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-5 border-r border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-3 h-3 rounded-full bg-amber-500"></div>
                            <span class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-bold bg-amber-100 text-amber-800 border-2 border-amber-300 shadow-sm">
                                📂 ${result.category}
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-5 text-sm font-semibold text-gray-900 border-r border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                            <span class="bg-gray-50 px-3 py-2 rounded-md border border-gray-200">${result.sub_category}</span>
                        </div>
                    </td>
                    <td class="px-6 py-5 text-sm font-medium text-gray-700 border-r border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                            <span class="bg-gray-50 px-3 py-2 rounded-md border border-gray-200">${result.sub_sub_category}</span>
                        </div>
                    </td>
                    <td class="px-6 py-5 text-sm text-gray-900 border-r border-gray-200">
                        <div class="bg-white p-3 rounded-lg border border-gray-200 shadow-sm">
                            <p class="text-sm leading-relaxed font-medium">${result.description}</p>
                        </div>
                    </td>
                    <td class="px-6 py-5 border-r border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-3 h-3 rounded-full bg-red-500 animate-pulse"></div>
                            <span class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-bold border-2 ${slaColor} shadow-sm">
                                ⏰ ${result.sla}
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-5 text-sm text-gray-700">
                        <div class="flex items-start gap-3">
                            <span class="text-yellow-500 text-lg">⚠️</span>
                            <div class="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
                                <span class="text-sm font-medium text-yellow-800">${result.notes}</span>
                            </div>
                        </div>
                    </td>
                </tr>
            `;

            resultsTableBody.innerHTML = row;

            // Create markdown preview
            const markdown = `| department | case_type | category | sub_category | sub_sub_category | description | SLA | notes |
| ---------- | --------- | -------- | ------------ | ---------------- | ----------- | --- | ----- |
| ${result.department} | ${result.case_type} | ${result.category} | ${result.sub_category} | ${result.sub_sub_category} | ${result.description} | ${result.sla} | ${result.notes} |`;

            markdownPreview.textContent = markdown;

            // Show results
            resultsSection.classList.remove('hidden');
        }
    </script>
</body>
</html>
