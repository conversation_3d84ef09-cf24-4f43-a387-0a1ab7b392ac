<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام تحليل مشاكل خدمة العملاء</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .loading {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto p-6 max-w-6xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">
                نظام تحليل مشاكل خدمة العملاء
            </h1>
            <p class="text-lg text-gray-600">
                أدخل وصف المشكلة باللغة العربية وسيتم تحليلها وتصنيفها تلقائياً
            </p>
        </div>

        <!-- Input Section -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <label for="issue-input" class="block text-lg font-medium text-gray-700 mb-4">
                وصف المشكلة (باللغة العربية)
            </label>
            <textarea
                id="issue-input"
                placeholder="مثال: العميل بيشتكي إن الحساب بتاعه اتمسح ومش قادر يرجع له تاني وبيقول كل البيانات اختفت..."
                class="w-full h-32 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                dir="rtl"
            ></textarea>
            
            <div class="flex justify-between items-center mt-4">
                <div class="text-sm text-gray-500">
                    عدد الأحرف: <span id="char-count">0</span>
                </div>
                <button
                    id="analyze-btn"
                    class="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                    <span id="btn-text">تحليل المشكلة</span>
                    <div id="loading-spinner" class="hidden w-5 h-5 border-2 border-white border-t-transparent rounded-full loading"></div>
                </button>
            </div>
        </div>

        <!-- Results Section -->
        <div id="results-section" class="hidden space-y-6">
            <!-- Success Message -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <span class="text-green-700">
                    تم تحليل المشكلة بنجاح! مستوى الثقة: <span id="confidence-score">0</span>%
                </span>
            </div>

            <!-- Translation -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 class="font-medium text-blue-900 mb-2">الترجمة الإنجليزية:</h3>
                <p id="english-translation" class="text-blue-800"></p>
            </div>

            <!-- Results Table -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-900">نتائج التحليل</h2>
                    <button
                        id="copy-btn"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        نسخ الجدول
                    </button>
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 border-r border-gray-200">Department</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 border-r border-gray-200">Case Type</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 border-r border-gray-200">Category</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 border-r border-gray-200">Sub Category</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 border-r border-gray-200">Sub Sub Category</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 border-r border-gray-200">Description</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 border-r border-gray-200">SLA</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">Notes</th>
                            </tr>
                        </thead>
                        <tbody id="results-table-body">
                            <!-- Results will be inserted here -->
                        </tbody>
                    </table>
                </div>

                <!-- Markdown Preview -->
                <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
                    <h3 class="text-sm font-medium text-gray-700 mb-2">معاينة الجدول (Markdown):</h3>
                    <div class="bg-white p-4 rounded border text-sm font-mono text-gray-600 overflow-x-auto">
                        <pre id="markdown-preview"></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Section -->
        <div id="error-section" class="hidden bg-red-50 border border-red-200 rounded-lg p-4">
            <span class="text-red-700" id="error-message"></span>
        </div>
    </div>

    <script>
        // DOM Elements
        const issueInput = document.getElementById('issue-input');
        const charCount = document.getElementById('char-count');
        const analyzeBtn = document.getElementById('analyze-btn');
        const btnText = document.getElementById('btn-text');
        const loadingSpinner = document.getElementById('loading-spinner');
        const resultsSection = document.getElementById('results-section');
        const errorSection = document.getElementById('error-section');
        const errorMessage = document.getElementById('error-message');
        const confidenceScore = document.getElementById('confidence-score');
        const englishTranslation = document.getElementById('english-translation');
        const resultsTableBody = document.getElementById('results-table-body');
        const markdownPreview = document.getElementById('markdown-preview');
        const copyBtn = document.getElementById('copy-btn');

        // Character count
        issueInput.addEventListener('input', function() {
            charCount.textContent = this.value.length;
        });

        // Analyze button click
        analyzeBtn.addEventListener('click', async function() {
            const arabicText = issueInput.value.trim();
            
            if (!arabicText) {
                showError('يرجى إدخال وصف المشكلة');
                return;
            }

            setLoading(true);
            hideError();
            hideResults();

            try {
                // Simulate API call with mock data for demo
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const mockResult = {
                    department: 'IT',
                    case_type: 'Account Problem',
                    category: 'Access',
                    sub_category: 'Account Deleted',
                    sub_sub_category: 'Data Loss',
                    description: 'Customer reports account deletion with complete data loss',
                    sla: '4 hours',
                    notes: 'Urgent - requires immediate attention',
                    english_translation: 'The customer is complaining that their account has been deleted and they cannot access it again, saying all data has disappeared',
                    confidence_score: 85
                };

                displayResults(mockResult);
            } catch (error) {
                showError('حدث خطأ أثناء تحليل المشكلة');
            } finally {
                setLoading(false);
            }
        });

        // Copy button click
        copyBtn.addEventListener('click', function() {
            const markdownText = markdownPreview.textContent;
            navigator.clipboard.writeText(markdownText).then(() => {
                copyBtn.textContent = 'تم النسخ!';
                setTimeout(() => {
                    copyBtn.textContent = 'نسخ الجدول';
                }, 2000);
            });
        });

        function setLoading(loading) {
            if (loading) {
                btnText.textContent = 'جاري التحليل...';
                loadingSpinner.classList.remove('hidden');
                analyzeBtn.disabled = true;
            } else {
                btnText.textContent = 'تحليل المشكلة';
                loadingSpinner.classList.add('hidden');
                analyzeBtn.disabled = false;
            }
        }

        function showError(message) {
            errorMessage.textContent = message;
            errorSection.classList.remove('hidden');
        }

        function hideError() {
            errorSection.classList.add('hidden');
        }

        function hideResults() {
            resultsSection.classList.add('hidden');
        }

        function displayResults(result) {
            // Update confidence score and translation
            confidenceScore.textContent = result.confidence_score;
            englishTranslation.textContent = result.english_translation;

            // Create table row
            const row = `
                <tr class="border-b border-gray-200 hover:bg-gray-50">
                    <td class="px-4 py-3 text-sm text-gray-900 border-r border-gray-200">
                        <span class="inline-block px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                            ${result.department}
                        </span>
                    </td>
                    <td class="px-4 py-3 text-sm text-gray-900 border-r border-gray-200">
                        <span class="inline-block px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                            ${result.case_type}
                        </span>
                    </td>
                    <td class="px-4 py-3 text-sm text-gray-900 border-r border-gray-200">
                        <span class="inline-block px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                            ${result.category}
                        </span>
                    </td>
                    <td class="px-4 py-3 text-sm text-gray-900 border-r border-gray-200">${result.sub_category}</td>
                    <td class="px-4 py-3 text-sm text-gray-900 border-r border-gray-200">${result.sub_sub_category}</td>
                    <td class="px-4 py-3 text-sm text-gray-900 border-r border-gray-200">${result.description}</td>
                    <td class="px-4 py-3 text-sm text-gray-900 border-r border-gray-200">
                        <span class="inline-block px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">
                            ${result.sla}
                        </span>
                    </td>
                    <td class="px-4 py-3 text-sm text-gray-900">${result.notes}</td>
                </tr>
            `;

            resultsTableBody.innerHTML = row;

            // Create markdown preview
            const markdown = `| department | case_type | category | sub_category | sub_sub_category | description | SLA | notes |
| ---------- | --------- | -------- | ------------ | ---------------- | ----------- | --- | ----- |
| ${result.department} | ${result.case_type} | ${result.category} | ${result.sub_category} | ${result.sub_sub_category} | ${result.description} | ${result.sla} | ${result.notes} |`;

            markdownPreview.textContent = markdown;

            // Show results
            resultsSection.classList.remove('hidden');
        }
    </script>
</body>
</html>
