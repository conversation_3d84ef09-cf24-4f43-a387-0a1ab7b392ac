<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام تحليل مشاكل خدمة العملاء</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .loading {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto p-6 max-w-6xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">
                نظام تحليل مشاكل خدمة العملاء
            </h1>
            <p class="text-lg text-gray-600">
                أدخل وصف المشكلة باللغة العربية وسيتم تحليلها وتصنيفها تلقائياً
            </p>
        </div>

        <!-- Input Section -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <label for="issue-input" class="block text-lg font-medium text-gray-700 mb-4">
                وصف المشكلة (باللغة العربية)
            </label>
            <textarea
                id="issue-input"
                placeholder="مثال: العميل بيشتكي إن الحساب بتاعه اتمسح ومش قادر يرجع له تاني وبيقول كل البيانات اختفت..."
                class="w-full h-32 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                dir="rtl"
            ></textarea>
            
            <div class="flex justify-between items-center mt-4">
                <div class="text-sm text-gray-500">
                    عدد الأحرف: <span id="char-count">0</span>
                </div>
                <button
                    id="analyze-btn"
                    class="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                    <span id="btn-text">تحليل المشكلة</span>
                    <div id="loading-spinner" class="hidden w-5 h-5 border-2 border-white border-t-transparent rounded-full loading"></div>
                </button>
            </div>
        </div>

        <!-- Results Section -->
        <div id="results-section" class="hidden space-y-6">
            <!-- Success Message -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <span class="text-green-700">
                    تم تحليل المشكلة بنجاح! مستوى الثقة: <span id="confidence-score">0</span>%
                </span>
            </div>

            <!-- Translation -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 class="font-medium text-blue-900 mb-2">الترجمة الإنجليزية:</h3>
                <p id="english-translation" class="text-blue-800"></p>
            </div>

            <!-- Results Table -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-900">نتائج التحليل</h2>
                    <button
                        id="copy-btn"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        نسخ الجدول
                    </button>
                </div>

                <div class="overflow-x-auto border-2 border-gray-300 rounded-xl shadow-lg">
                    <table class="w-full border-collapse bg-white">
                        <thead>
                            <tr class="bg-gradient-to-r from-blue-600 to-blue-700 text-white">
                                <th class="px-6 py-4 text-left text-sm font-bold border-r-2 border-blue-500 min-w-[120px]">
                                    <div class="flex items-center gap-2">
                                        🏢 Department
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r-2 border-blue-500 min-w-[140px]">
                                    <div class="flex items-center gap-2">
                                        📋 Case Type
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r-2 border-blue-500 min-w-[120px]">
                                    <div class="flex items-center gap-2">
                                        📂 Category
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r-2 border-blue-500 min-w-[140px]">
                                    <div class="flex items-center gap-2">
                                        📁 Sub Category
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r-2 border-blue-500 min-w-[160px]">
                                    <div class="flex items-center gap-2">
                                        🗂️ Sub Sub Category
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r-2 border-blue-500 min-w-[200px]">
                                    <div class="flex items-center gap-2">
                                        📝 Description
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold border-r-2 border-blue-500 min-w-[100px]">
                                    <div class="flex items-center gap-2">
                                        ⏰ SLA
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-sm font-bold min-w-[150px]">
                                    <div class="flex items-center gap-2">
                                        📌 Notes
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="results-table-body" class="divide-y-2 divide-gray-200">
                            <!-- Results will be inserted here -->
                        </tbody>
                    </table>
                </div>

                <!-- Markdown Preview -->
                <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
                    <h3 class="text-sm font-medium text-gray-700 mb-2">معاينة الجدول (Markdown):</h3>
                    <div class="bg-white p-4 rounded border text-sm font-mono text-gray-600 overflow-x-auto">
                        <pre id="markdown-preview"></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Section -->
        <div id="error-section" class="hidden bg-red-50 border border-red-200 rounded-lg p-4">
            <span class="text-red-700" id="error-message"></span>
        </div>
    </div>

    <script>
        // DOM Elements
        const issueInput = document.getElementById('issue-input');
        const charCount = document.getElementById('char-count');
        const analyzeBtn = document.getElementById('analyze-btn');
        const btnText = document.getElementById('btn-text');
        const loadingSpinner = document.getElementById('loading-spinner');
        const resultsSection = document.getElementById('results-section');
        const errorSection = document.getElementById('error-section');
        const errorMessage = document.getElementById('error-message');
        const confidenceScore = document.getElementById('confidence-score');
        const englishTranslation = document.getElementById('english-translation');
        const resultsTableBody = document.getElementById('results-table-body');
        const markdownPreview = document.getElementById('markdown-preview');
        const copyBtn = document.getElementById('copy-btn');

        // Character count
        issueInput.addEventListener('input', function() {
            charCount.textContent = this.value.length;
        });

        // Analyze button click
        analyzeBtn.addEventListener('click', async function() {
            const arabicText = issueInput.value.trim();
            
            if (!arabicText) {
                showError('يرجى إدخال وصف المشكلة');
                return;
            }

            setLoading(true);
            hideError();
            hideResults();

            try {
                // Simulate API call with mock data for demo
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const mockResult = {
                    department: 'IT',
                    case_type: 'Account Problem',
                    category: 'Access',
                    sub_category: 'Account Deleted',
                    sub_sub_category: 'Data Loss',
                    description: 'Customer reports account deletion with complete data loss',
                    sla: '4 hours',
                    notes: 'Urgent - requires immediate attention',
                    english_translation: 'The customer is complaining that their account has been deleted and they cannot access it again, saying all data has disappeared',
                    confidence_score: 85
                };

                displayResults(mockResult);
            } catch (error) {
                showError('حدث خطأ أثناء تحليل المشكلة');
            } finally {
                setLoading(false);
            }
        });

        // Copy button click
        copyBtn.addEventListener('click', function() {
            const markdownText = markdownPreview.textContent;
            navigator.clipboard.writeText(markdownText).then(() => {
                copyBtn.textContent = 'تم النسخ!';
                setTimeout(() => {
                    copyBtn.textContent = 'نسخ الجدول';
                }, 2000);
            });
        });

        function setLoading(loading) {
            if (loading) {
                btnText.textContent = 'جاري التحليل...';
                loadingSpinner.classList.remove('hidden');
                analyzeBtn.disabled = true;
            } else {
                btnText.textContent = 'تحليل المشكلة';
                loadingSpinner.classList.add('hidden');
                analyzeBtn.disabled = false;
            }
        }

        function showError(message) {
            errorMessage.textContent = message;
            errorSection.classList.remove('hidden');
        }

        function hideError() {
            errorSection.classList.add('hidden');
        }

        function hideResults() {
            resultsSection.classList.add('hidden');
        }

        function displayResults(result) {
            // Update confidence score and translation
            confidenceScore.textContent = result.confidence_score;
            englishTranslation.textContent = result.english_translation;

            // Get color schemes for different elements
            const departmentColors = {
                'IT': 'bg-blue-100 text-blue-800 border-blue-300',
                'Finance': 'bg-green-100 text-green-800 border-green-300',
                'HR': 'bg-purple-100 text-purple-800 border-purple-300',
                'Logistics': 'bg-orange-100 text-orange-800 border-orange-300',
                'Customer Service': 'bg-pink-100 text-pink-800 border-pink-300',
                'Sales': 'bg-indigo-100 text-indigo-800 border-indigo-300'
            };

            const slaColors = {
                '1 hour': 'bg-red-100 text-red-800 border-red-300',
                '2 hours': 'bg-orange-100 text-orange-800 border-orange-300',
                '4 hours': 'bg-yellow-100 text-yellow-800 border-yellow-300',
                '12 hours': 'bg-blue-100 text-blue-800 border-blue-300',
                '24 hours': 'bg-green-100 text-green-800 border-green-300',
                '48 hours': 'bg-gray-100 text-gray-800 border-gray-300'
            };

            const deptColor = departmentColors[result.department] || 'bg-gray-100 text-gray-800 border-gray-300';
            const slaColor = slaColors[result.sla] || 'bg-gray-100 text-gray-800 border-gray-300';

            // Create enhanced table row with beautiful styling
            const row = `
                <tr class="hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 border-b-2 border-gray-200">
                    <td class="px-6 py-5 border-r-2 border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-3 h-3 rounded-full bg-blue-500 animate-pulse"></div>
                            <span class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-bold border-2 ${deptColor} shadow-md">
                                🏢 ${result.department}
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-5 border-r-2 border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-3 h-3 rounded-full bg-emerald-500 animate-pulse"></div>
                            <span class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-bold bg-emerald-100 text-emerald-800 border-2 border-emerald-300 shadow-md">
                                📋 ${result.case_type}
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-5 border-r-2 border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-3 h-3 rounded-full bg-amber-500 animate-pulse"></div>
                            <span class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-bold bg-amber-100 text-amber-800 border-2 border-amber-300 shadow-md">
                                📂 ${result.category}
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-5 text-sm font-semibold text-gray-900 border-r-2 border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                            <span class="bg-gray-50 px-4 py-2 rounded-lg border-2 border-gray-200 shadow-sm font-medium">${result.sub_category}</span>
                        </div>
                    </td>
                    <td class="px-6 py-5 text-sm font-medium text-gray-700 border-r-2 border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                            <span class="bg-gray-50 px-4 py-2 rounded-lg border-2 border-gray-200 shadow-sm font-medium">${result.sub_sub_category}</span>
                        </div>
                    </td>
                    <td class="px-6 py-5 text-sm text-gray-900 border-r-2 border-gray-200">
                        <div class="bg-white p-4 rounded-lg border-2 border-gray-200 shadow-md">
                            <p class="text-sm leading-relaxed font-medium">${result.description}</p>
                        </div>
                    </td>
                    <td class="px-6 py-5 border-r-2 border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-3 h-3 rounded-full bg-red-500 animate-pulse"></div>
                            <span class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-bold border-2 ${slaColor} shadow-md">
                                ⏰ ${result.sla}
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-5 text-sm text-gray-700">
                        <div class="flex items-start gap-3">
                            <span class="text-yellow-500 text-lg">⚠️</span>
                            <div class="bg-yellow-50 p-4 rounded-lg border-2 border-yellow-200 shadow-md">
                                <span class="text-sm font-medium text-yellow-800">${result.notes}</span>
                            </div>
                        </div>
                    </td>
                </tr>
            `;

            resultsTableBody.innerHTML = row;

            // Create markdown preview
            const markdown = `| department | case_type | category | sub_category | sub_sub_category | description | SLA | notes |
| ---------- | --------- | -------- | ------------ | ---------------- | ----------- | --- | ----- |
| ${result.department} | ${result.case_type} | ${result.category} | ${result.sub_category} | ${result.sub_sub_category} | ${result.description} | ${result.sla} | ${result.notes} |`;

            markdownPreview.textContent = markdown;

            // Show results
            resultsSection.classList.remove('hidden');
        }
    </script>
</body>
</html>
