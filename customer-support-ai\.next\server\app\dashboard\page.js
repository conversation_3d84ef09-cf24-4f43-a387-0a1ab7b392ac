(()=>{var t={};t.id=105,t.ids=[105],t.modules={78:()=>{},440:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>r});var s=i(1658);let r=async t=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await t.params,"favicon.ico")+""}]},501:(t,e,i)=>{Promise.resolve().then(i.bind(i,7590)),Promise.resolve().then(i.bind(i,7881))},559:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>o});var s=i(7413),r=i(1227),n=i(5625);function o(){return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(r.default,{}),(0,s.jsx)(n.Toaster,{position:"top-center",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"}}})]})}},846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},849:()=>{},1135:()=>{},1151:(t,e,i)=>{"use strict";i.r(e),i.d(e,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>h,routeModule:()=>c,tree:()=>u});var s=i(5239),r=i(8088),n=i(8170),o=i.n(n),a=i(893),l={};for(let t in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(l[t]=()=>a[t]);i.d(e,l);let u={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,559)),"C:\\Users\\<USER>\\Desktop\\senario casses\\customer-support-ai\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,4431)),"C:\\Users\\<USER>\\Desktop\\senario casses\\customer-support-ai\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,h=["C:\\Users\\<USER>\\Desktop\\senario casses\\customer-support-ai\\src\\app\\dashboard\\page.tsx"],d={require:i,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},1227:(t,e,i)=>{"use strict";i.d(e,{default:()=>s});let s=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\senario casses\\\\customer-support-ai\\\\src\\\\components\\\\CasesDashboard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\senario casses\\customer-support-ai\\src\\components\\CasesDashboard.tsx","default")},3033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3315:(t,e,i)=>{Promise.resolve().then(i.t.bind(i,6444,23)),Promise.resolve().then(i.t.bind(i,6042,23)),Promise.resolve().then(i.t.bind(i,8170,23)),Promise.resolve().then(i.t.bind(i,9477,23)),Promise.resolve().then(i.t.bind(i,9345,23)),Promise.resolve().then(i.t.bind(i,2089,23)),Promise.resolve().then(i.t.bind(i,6577,23)),Promise.resolve().then(i.t.bind(i,1307,23))},3873:t=>{"use strict";t.exports=require("path")},4053:(t,e,i)=>{Promise.resolve().then(i.bind(i,5625)),Promise.resolve().then(i.bind(i,1227))},4431:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>u,metadata:()=>l});var s=i(7413),r=i(2376),n=i.n(r),o=i(8726),a=i.n(o);i(1135);let l={title:"نظام تحليل مشاكل خدمة العملاء | Customer Support AI",description:"نظام ذكي لتحليل وتصنيف مشاكل خدمة العملاء باللغة العربية باستخدام الذكاء الاصطناعي"};function u({children:t}){return(0,s.jsx)("html",{lang:"ar",dir:"rtl",children:(0,s.jsx)("body",{className:`${n().variable} ${a().variable} antialiased`,children:t})})}},5625:(t,e,i)=>{"use strict";i.d(e,{Toaster:()=>r});var s=i(2907);(0,s.registerClientReference)(function(){throw Error("Attempted to call CheckmarkIcon() from the server but CheckmarkIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\senario casses\\customer-support-ai\\node_modules\\react-hot-toast\\dist\\index.mjs","CheckmarkIcon"),(0,s.registerClientReference)(function(){throw Error("Attempted to call ErrorIcon() from the server but ErrorIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\senario casses\\customer-support-ai\\node_modules\\react-hot-toast\\dist\\index.mjs","ErrorIcon"),(0,s.registerClientReference)(function(){throw Error("Attempted to call LoaderIcon() from the server but LoaderIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\senario casses\\customer-support-ai\\node_modules\\react-hot-toast\\dist\\index.mjs","LoaderIcon"),(0,s.registerClientReference)(function(){throw Error("Attempted to call ToastBar() from the server but ToastBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\senario casses\\customer-support-ai\\node_modules\\react-hot-toast\\dist\\index.mjs","ToastBar"),(0,s.registerClientReference)(function(){throw Error("Attempted to call ToastIcon() from the server but ToastIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\senario casses\\customer-support-ai\\node_modules\\react-hot-toast\\dist\\index.mjs","ToastIcon");let r=(0,s.registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\senario casses\\customer-support-ai\\node_modules\\react-hot-toast\\dist\\index.mjs","Toaster");(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\senario casses\\\\customer-support-ai\\\\node_modules\\\\react-hot-toast\\\\dist\\\\index.mjs\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\senario casses\\customer-support-ai\\node_modules\\react-hot-toast\\dist\\index.mjs","default"),(0,s.registerClientReference)(function(){throw Error("Attempted to call resolveValue() from the server but resolveValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\senario casses\\customer-support-ai\\node_modules\\react-hot-toast\\dist\\index.mjs","resolveValue"),(0,s.registerClientReference)(function(){throw Error("Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\senario casses\\customer-support-ai\\node_modules\\react-hot-toast\\dist\\index.mjs","toast"),(0,s.registerClientReference)(function(){throw Error("Attempted to call useToaster() from the server but useToaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\senario casses\\customer-support-ai\\node_modules\\react-hot-toast\\dist\\index.mjs","useToaster"),(0,s.registerClientReference)(function(){throw Error("Attempted to call useToasterStore() from the server but useToasterStore is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\senario casses\\customer-support-ai\\node_modules\\react-hot-toast\\dist\\index.mjs","useToasterStore")},7590:(t,e,i)=>{"use strict";i.d(e,{Toaster:()=>tu});var s,r=i(3210);let n={data:""},o=t=>"object"==typeof window?((t?t.querySelector("#_goober"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:t||n,a=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,l=/\/\*[^]*?\*\/|  +/g,u=/\n+/g,h=(t,e)=>{let i="",s="",r="";for(let n in t){let o=t[n];"@"==n[0]?"i"==n[1]?i=n+" "+o+";":s+="f"==n[1]?h(o,n):n+"{"+h(o,"k"==n[1]?"":e)+"}":"object"==typeof o?s+=h(o,e?e.replace(/([^,])+/g,t=>n.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,e=>/&/.test(e)?e.replace(/&/g,t):t?t+" "+e:e)):n):null!=o&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,"-$&").toLowerCase(),r+=h.p?h.p(n,o):n+":"+o+";")}return i+(e&&r?e+"{"+r+"}":r)+s},d={},c=t=>{if("object"==typeof t){let e="";for(let i in t)e+=i+c(t[i]);return e}return t},p=(t,e,i,s,r)=>{let n=c(t),o=d[n]||(d[n]=(t=>{let e=0,i=11;for(;e<t.length;)i=101*i+t.charCodeAt(e++)>>>0;return"go"+i})(n));if(!d[o]){let e=n!==t?t:(t=>{let e,i,s=[{}];for(;e=a.exec(t.replace(l,""));)e[4]?s.shift():e[3]?(i=e[3].replace(u," ").trim(),s.unshift(s[0][i]=s[0][i]||{})):s[0][e[1]]=e[2].replace(u," ").trim();return s[0]})(t);d[o]=h(r?{["@keyframes "+o]:e}:e,i?"":"."+o)}let p=i&&d.g?d.g:null;return i&&(d.g=d[o]),((t,e,i,s)=>{s?e.data=e.data.replace(s,t):-1===e.data.indexOf(t)&&(e.data=i?t+e.data:e.data+t)})(d[o],e,s,p),o},m=(t,e,i)=>t.reduce((t,s,r)=>{let n=e[r];if(n&&n.call){let t=n(i),e=t&&t.props&&t.props.className||/^go/.test(t)&&t;n=e?"."+e:t&&"object"==typeof t?t.props?"":h(t,""):!1===t?"":t}return t+s+(null==n?"":n)},"");function f(t){let e=this||{},i=t.call?t(e.p):t;return p(i.unshift?i.raw?m(i,[].slice.call(arguments,1),e.p):i.reduce((t,i)=>Object.assign(t,i&&i.call?i(e.p):i),{}):i,o(e.target),e.g,e.o,e.k)}f.bind({g:1});let g,v,y,x=f.bind({k:1});function b(t,e){let i=this||{};return function(){let s=arguments;function r(n,o){let a=Object.assign({},n),l=a.className||r.className;i.p=Object.assign({theme:v&&v()},a),i.o=/ *go\d+/.test(l),a.className=f.apply(i,s)+(l?" "+l:""),e&&(a.ref=o);let u=t;return t[0]&&(u=a.as||t,delete a.as),y&&u[0]&&y(a),g(u,a)}return e?e(r):r}}var w=t=>"function"==typeof t,P=(t,e)=>w(t)?t(e):t,T=(()=>{let t=0;return()=>(++t).toString()})(),A=(()=>{let t;return()=>t})(),S=(t,e)=>{switch(e.type){case 0:return{...t,toasts:[e.toast,...t.toasts].slice(0,20)};case 1:return{...t,toasts:t.toasts.map(t=>t.id===e.toast.id?{...t,...e.toast}:t)};case 2:let{toast:i}=e;return S(t,{type:+!!t.toasts.find(t=>t.id===i.id),toast:i});case 3:let{toastId:s}=e;return{...t,toasts:t.toasts.map(t=>t.id===s||void 0===s?{...t,dismissed:!0,visible:!1}:t)};case 4:return void 0===e.toastId?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(t=>t.id!==e.toastId)};case 5:return{...t,pausedAt:e.time};case 6:let r=e.time-(t.pausedAt||0);return{...t,pausedAt:void 0,toasts:t.toasts.map(t=>({...t,pauseDuration:t.pauseDuration+r}))}}},C=[],k={toasts:[],pausedAt:void 0},E=t=>{k=S(k,t),C.forEach(t=>{t(k)})},j={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},D=(t={})=>{let[e,i]=(0,r.useState)(k),s=(0,r.useRef)(k);(0,r.useEffect)(()=>(s.current!==k&&i(k),C.push(i),()=>{let t=C.indexOf(i);t>-1&&C.splice(t,1)}),[]);let n=e.toasts.map(e=>{var i,s,r;return{...t,...t[e.type],...e,removeDelay:e.removeDelay||(null==(i=t[e.type])?void 0:i.removeDelay)||(null==t?void 0:t.removeDelay),duration:e.duration||(null==(s=t[e.type])?void 0:s.duration)||(null==t?void 0:t.duration)||j[e.type],style:{...t.style,...null==(r=t[e.type])?void 0:r.style,...e.style}}});return{...e,toasts:n}},M=(t,e="blank",i)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:e,ariaProps:{role:"status","aria-live":"polite"},message:t,pauseDuration:0,...i,id:(null==i?void 0:i.id)||T()}),V=t=>(e,i)=>{let s=M(e,t,i);return E({type:2,toast:s}),s.id},R=(t,e)=>V("blank")(t,e);R.error=V("error"),R.success=V("success"),R.loading=V("loading"),R.custom=V("custom"),R.dismiss=t=>{E({type:3,toastId:t})},R.remove=t=>E({type:4,toastId:t}),R.promise=(t,e,i)=>{let s=R.loading(e.loading,{...i,...null==i?void 0:i.loading});return"function"==typeof t&&(t=t()),t.then(t=>{let r=e.success?P(e.success,t):void 0;return r?R.success(r,{id:s,...i,...null==i?void 0:i.success}):R.dismiss(s),t}).catch(t=>{let r=e.error?P(e.error,t):void 0;r?R.error(r,{id:s,...i,...null==i?void 0:i.error}):R.dismiss(s)}),t};var L=(t,e)=>{E({type:1,toast:{id:t,height:e}})},F=()=>{E({type:5,time:Date.now()})},N=new Map,B=1e3,I=(t,e=B)=>{if(N.has(t))return;let i=setTimeout(()=>{N.delete(t),E({type:4,toastId:t})},e);N.set(t,i)},O=t=>{let{toasts:e,pausedAt:i}=D(t);(0,r.useEffect)(()=>{if(i)return;let t=Date.now(),s=e.map(e=>{if(e.duration===1/0)return;let i=(e.duration||0)+e.pauseDuration-(t-e.createdAt);if(i<0){e.visible&&R.dismiss(e.id);return}return setTimeout(()=>R.dismiss(e.id),i)});return()=>{s.forEach(t=>t&&clearTimeout(t))}},[e,i]);let s=(0,r.useCallback)(()=>{i&&E({type:6,time:Date.now()})},[i]),n=(0,r.useCallback)((t,i)=>{let{reverseOrder:s=!1,gutter:r=8,defaultPosition:n}=i||{},o=e.filter(e=>(e.position||n)===(t.position||n)&&e.height),a=o.findIndex(e=>e.id===t.id),l=o.filter((t,e)=>e<a&&t.visible).length;return o.filter(t=>t.visible).slice(...s?[l+1]:[0,l]).reduce((t,e)=>t+(e.height||0)+r,0)},[e]);return(0,r.useEffect)(()=>{e.forEach(t=>{if(t.dismissed)I(t.id,t.removeDelay);else{let e=N.get(t.id);e&&(clearTimeout(e),N.delete(t.id))}})},[e]),{toasts:e,handlers:{updateHeight:L,startPause:F,endPause:s,calculateOffset:n}}},U=x`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,$=x`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,z=x`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,W=b("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${U} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${$} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${t=>t.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${z} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,_=x`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,q=b("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${t=>t.secondary||"#e0e0e0"};
  border-right-color: ${t=>t.primary||"#616161"};
  animation: ${_} 1s linear infinite;
`,H=x`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Y=x`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,X=b("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${H} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Y} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${t=>t.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,K=b("div")`
  position: absolute;
`,G=b("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Z=x`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,J=b("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Z} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Q=({toast:t})=>{let{icon:e,type:i,iconTheme:s}=t;return void 0!==e?"string"==typeof e?r.createElement(J,null,e):e:"blank"===i?null:r.createElement(G,null,r.createElement(q,{...s}),"loading"!==i&&r.createElement(K,null,"error"===i?r.createElement(W,{...s}):r.createElement(X,{...s})))},tt=t=>`
0% {transform: translate3d(0,${-200*t}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,te=t=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*t}%,-1px) scale(.6); opacity:0;}
`,ti=b("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,ts=b("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,tr=(t,e)=>{let i=t.includes("top")?1:-1,[s,r]=A()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[tt(i),te(i)];return{animation:e?`${x(s)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${x(r)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},tn=r.memo(({toast:t,position:e,style:i,children:s})=>{let n=t.height?tr(t.position||e||"top-center",t.visible):{opacity:0},o=r.createElement(Q,{toast:t}),a=r.createElement(ts,{...t.ariaProps},P(t.message,t));return r.createElement(ti,{className:t.className,style:{...n,...i,...t.style}},"function"==typeof s?s({icon:o,message:a}):r.createElement(r.Fragment,null,o,a))});s=r.createElement,h.p=void 0,g=s,v=void 0,y=void 0;var to=({id:t,className:e,style:i,onHeightUpdate:s,children:n})=>{let o=r.useCallback(e=>{if(e){let i=()=>{s(t,e.getBoundingClientRect().height)};i(),new MutationObserver(i).observe(e,{subtree:!0,childList:!0,characterData:!0})}},[t,s]);return r.createElement("div",{ref:o,className:e,style:i},n)},ta=(t,e)=>{let i=t.includes("top"),s=t.includes("center")?{justifyContent:"center"}:t.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:A()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${e*(i?1:-1)}px)`,...i?{top:0}:{bottom:0},...s}},tl=f`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,tu=({reverseOrder:t,position:e="top-center",toastOptions:i,gutter:s,children:n,containerStyle:o,containerClassName:a})=>{let{toasts:l,handlers:u}=O(i);return r.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...o},className:a,onMouseEnter:u.startPause,onMouseLeave:u.endPause},l.map(i=>{let o=i.position||e,a=ta(o,u.calculateOffset(i,{reverseOrder:t,gutter:s,defaultPosition:e}));return r.createElement(to,{id:i.id,key:i.id,onHeightUpdate:u.updateHeight,className:i.visible?tl:"",style:a},"custom"===i.type?P(i.message,i):n?n(i):r.createElement(tn,{toast:i,position:o}))}))}},7881:(t,e,i)=>{"use strict";let s;i.d(e,{default:()=>nk});var r,n,o=i(687),a=i(3210);function l(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}let u=t=>Array.isArray(t);function h(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function d(t){return"string"==typeof t||Array.isArray(t)}function c(t){let e=[{},{}];return null==t||t.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function p(t,e,i,s){if("function"==typeof e){let[r,n]=c(s);e=e(void 0!==i?i:t.custom,r,n)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,n]=c(s);e=e(void 0!==i?i:t.custom,r,n)}return e}function m(t,e,i){let s=t.getProps();return p(s,e,void 0!==i?i:s.custom,t)}let f=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],g=["initial",...f];function v(t){let e;return()=>(void 0===e&&(e=t()),e)}let y=v(()=>void 0!==window.ScrollTimeline);class x{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>y()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class b extends x{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function w(t,e){return t?t[e]||t.default||t:void 0}function P(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function T(t){return"function"==typeof t}function A(t,e){t.timeline=e,t.onfinish=null}let S=t=>Array.isArray(t)&&"number"==typeof t[0],C={linearEasing:void 0},k=function(t,e){let i=v(t);return()=>{var t;return null!=(t=C[e])?t:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),E=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s},j=(t,e,i=10)=>{let s="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)s+=t(E(0,r-1,e))+", ";return`linear(${s.substring(0,s.length-2)})`},D=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,M={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:D([0,.65,.55,1]),circOut:D([.55,0,1,.45]),backIn:D([.31,.01,.66,-.59]),backOut:D([.33,1.53,.69,.99])},V={x:!1,y:!1};function R(t,e){let i=function(t,e,i){if(t instanceof Element)return[t];if("string"==typeof t){let e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function L(t){return e=>{"touch"===e.pointerType||V.x||V.y||t(e)}}let F=(t,e)=>!!e&&(t===e||F(t,e.parentElement)),N=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,B=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),I=new WeakSet;function O(t){return e=>{"Enter"===e.key&&t(e)}}function U(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let $=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=O(()=>{if(I.has(i))return;U(i,"down");let t=O(()=>{U(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>U(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function z(t){return N(t)&&!(V.x||V.y)}let W=t=>1e3*t,_=t=>t/1e3,q=t=>t,H=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Y=new Set(H),X=new Set(["width","height","top","left","right","bottom",...H]),K=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),G=t=>u(t)?t[t.length-1]||0:t,Z={skipAnimations:!1,useManualTiming:!1},J=["read","resolveKeyframes","update","preRender","render","postRender"];function Q(t,e){let i=!1,s=!0,r={delta:0,timestamp:0,isProcessing:!1},n=()=>i=!0,o=J.reduce((t,e)=>(t[e]=function(t){let e=new Set,i=new Set,s=!1,r=!1,n=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1};function a(e){n.has(e)&&(l.schedule(e),t()),e(o)}let l={schedule:(t,r=!1,o=!1)=>{let a=o&&s?e:i;return r&&n.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),n.delete(t)},process:t=>{if(o=t,s){r=!0;return}s=!0,[e,i]=[i,e],e.forEach(a),e.clear(),s=!1,r&&(r=!1,l.process(t))}};return l}(n),t),{}),{read:a,resolveKeyframes:l,update:u,preRender:h,render:d,postRender:c}=o,p=()=>{let n=Z.useManualTiming?r.timestamp:performance.now();i=!1,r.delta=s?1e3/60:Math.max(Math.min(n-r.timestamp,40),1),r.timestamp=n,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),h.process(r),d.process(r),c.process(r),r.isProcessing=!1,i&&e&&(s=!1,t(p))},m=()=>{i=!0,s=!0,r.isProcessing||t(p)};return{schedule:J.reduce((t,e)=>{let s=o[e];return t[e]=(t,e=!1,r=!1)=>(i||m(),s.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<J.length;e++)o[J[e]].cancel(t)},state:r,steps:o}}let{schedule:tt,cancel:te,state:ti,steps:ts}=Q("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:q,!0);function tr(){s=void 0}let tn={now:()=>(void 0===s&&tn.set(ti.isProcessing||Z.useManualTiming?ti.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(tr)}};function to(t,e){-1===t.indexOf(e)&&t.push(e)}function ta(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class tl{constructor(){this.subscriptions=[]}add(t){return to(this.subscriptions,t),()=>ta(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,i);else for(let r=0;r<s;r++){let s=this.subscriptions[r];s&&s(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let tu=t=>!isNaN(parseFloat(t)),th={current:void 0};class td{constructor(t,e={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=tn.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=tn.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=tu(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new tl);let i=this.events[t].add(e);return"change"===t?()=>{i(),tt.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return th.current&&th.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=tn.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function tc(t,e){return new td(t,e)}let tp=t=>!!(t&&t.getVelocity);function tm(t,e){let i=t.getValue("willChange");if(tp(i)&&i.add)return i.add(e)}let tf=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tg="data-"+tf("framerAppearId"),tv={current:!1},ty=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tx(t,e,i,s){if(t===e&&i===s)return q;let r=e=>(function(t,e,i,s,r){let n,o,a=0;do(n=ty(o=e+(i-e)/2,s,r)-t)>0?i=o:e=o;while(Math.abs(n)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:ty(r(t),e,s)}let tb=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,tw=t=>e=>1-t(1-e),tP=tx(.33,1.53,.69,.99),tT=tw(tP),tA=tb(tT),tS=t=>(t*=2)<1?.5*tT(t):.5*(2-Math.pow(2,-10*(t-1))),tC=t=>1-Math.sin(Math.acos(t)),tk=tw(tC),tE=tb(tC),tj=t=>/^0[^.\s]+$/u.test(t),tD=(t,e,i)=>i>e?e:i<t?t:i,tM={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},tV={...tM,transform:t=>tD(0,1,t)},tR={...tM,default:1},tL=t=>Math.round(1e5*t)/1e5,tF=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tN=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tB=(t,e)=>i=>!!("string"==typeof i&&tN.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tI=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[r,n,o,a]=s.match(tF);return{[t]:parseFloat(r),[e]:parseFloat(n),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tO=t=>tD(0,255,t),tU={...tM,transform:t=>Math.round(tO(t))},t$={test:tB("rgb","red"),parse:tI("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+tU.transform(t)+", "+tU.transform(e)+", "+tU.transform(i)+", "+tL(tV.transform(s))+")"},tz={test:tB("#"),parse:function(t){let e="",i="",s="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,s+=s,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:r?parseInt(r,16)/255:1}},transform:t$.transform},tW=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),t_=tW("deg"),tq=tW("%"),tH=tW("px"),tY=tW("vh"),tX=tW("vw"),tK={...tq,parse:t=>tq.parse(t)/100,transform:t=>tq.transform(100*t)},tG={test:tB("hsl","hue"),parse:tI("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:s=1})=>"hsla("+Math.round(t)+", "+tq.transform(tL(e))+", "+tq.transform(tL(i))+", "+tL(tV.transform(s))+")"},tZ={test:t=>t$.test(t)||tz.test(t)||tG.test(t),parse:t=>t$.test(t)?t$.parse(t):tG.test(t)?tG.parse(t):tz.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?t$.transform(t):tG.transform(t)},tJ=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tQ="number",t0="color",t1=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function t2(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},r=[],n=0,o=e.replace(t1,t=>(tZ.test(t)?(s.color.push(n),r.push(t0),i.push(tZ.parse(t))):t.startsWith("var(")?(s.var.push(n),r.push("var"),i.push(t)):(s.number.push(n),r.push(tQ),i.push(parseFloat(t))),++n,"${}")).split("${}");return{values:i,split:o,indexes:s,types:r}}function t3(t){return t2(t).values}function t5(t){let{split:e,types:i}=t2(t),s=e.length;return t=>{let r="";for(let n=0;n<s;n++)if(r+=e[n],void 0!==t[n]){let e=i[n];e===tQ?r+=tL(t[n]):e===t0?r+=tZ.transform(t[n]):r+=t[n]}return r}}let t4=t=>"number"==typeof t?0:t,t9={test:function(t){var e,i;return isNaN(t)&&"string"==typeof t&&((null==(e=t.match(tF))?void 0:e.length)||0)+((null==(i=t.match(tJ))?void 0:i.length)||0)>0},parse:t3,createTransformer:t5,getAnimatableNone:function(t){let e=t3(t);return t5(t)(e.map(t4))}},t6=new Set(["brightness","contrast","saturate","opacity"]);function t8(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(tF)||[];if(!s)return t;let r=i.replace(s,""),n=+!!t6.has(e);return s!==i&&(n*=100),e+"("+n+r+")"}let t7=/\b([a-z-]*)\(.*?\)/gu,et={...t9,getAnimatableNone:t=>{let e=t.match(t7);return e?e.map(t8).join(" "):t}},ee={...tM,transform:Math.round},ei={borderWidth:tH,borderTopWidth:tH,borderRightWidth:tH,borderBottomWidth:tH,borderLeftWidth:tH,borderRadius:tH,radius:tH,borderTopLeftRadius:tH,borderTopRightRadius:tH,borderBottomRightRadius:tH,borderBottomLeftRadius:tH,width:tH,maxWidth:tH,height:tH,maxHeight:tH,top:tH,right:tH,bottom:tH,left:tH,padding:tH,paddingTop:tH,paddingRight:tH,paddingBottom:tH,paddingLeft:tH,margin:tH,marginTop:tH,marginRight:tH,marginBottom:tH,marginLeft:tH,backgroundPositionX:tH,backgroundPositionY:tH,rotate:t_,rotateX:t_,rotateY:t_,rotateZ:t_,scale:tR,scaleX:tR,scaleY:tR,scaleZ:tR,skew:t_,skewX:t_,skewY:t_,distance:tH,translateX:tH,translateY:tH,translateZ:tH,x:tH,y:tH,z:tH,perspective:tH,transformPerspective:tH,opacity:tV,originX:tK,originY:tK,originZ:tH,zIndex:ee,size:tH,fillOpacity:tV,strokeOpacity:tV,numOctaves:ee},es={...ei,color:tZ,backgroundColor:tZ,outlineColor:tZ,fill:tZ,stroke:tZ,borderColor:tZ,borderTopColor:tZ,borderRightColor:tZ,borderBottomColor:tZ,borderLeftColor:tZ,filter:et,WebkitFilter:et},er=t=>es[t];function en(t,e){let i=er(t);return i!==et&&(i=t9),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let eo=new Set(["auto","none","0"]),ea=t=>t===tM||t===tH,el=(t,e)=>parseFloat(t.split(", ")[e]),eu=(t,e)=>(i,{transform:s})=>{if("none"===s||!s)return 0;let r=s.match(/^matrix3d\((.+)\)$/u);if(r)return el(r[1],e);{let e=s.match(/^matrix\((.+)\)$/u);return e?el(e[1],t):0}},eh=new Set(["x","y","z"]),ed=H.filter(t=>!eh.has(t)),ec={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:eu(4,13),y:eu(5,14)};ec.translateX=ec.x,ec.translateY=ec.y;let ep=new Set,em=!1,ef=!1;function eg(){if(ef){let t=Array.from(ep).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ed.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{var s;null==(s=t.getValue(e))||s.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}ef=!1,em=!1,ep.forEach(t=>t.complete()),ep.clear()}function ev(){ep.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(ef=!0)})}class ey{constructor(t,e,i,s,r,n=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=r,this.isAsync=n}scheduleResolve(){this.isScheduled=!0,this.isAsync?(ep.add(this),em||(em=!0,tt.read(ev),tt.resolveKeyframes(eg))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;for(let r=0;r<t.length;r++)if(null===t[r])if(0===r){let r=null==s?void 0:s.get(),n=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let s=i.readValue(e,n);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=n),s&&void 0===r&&s.set(t[0])}else t[r]=t[r-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),ep.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,ep.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let ex=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),eb=t=>e=>"string"==typeof e&&e.startsWith(t),ew=eb("--"),eP=eb("var(--"),eT=t=>!!eP(t)&&eA.test(t.split("/*")[0].trim()),eA=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,eS=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,eC=t=>e=>e.test(t),ek=[tM,tH,tq,t_,tX,tY,{test:t=>"auto"===t,parse:t=>t}],eE=t=>ek.find(eC(t));class ej extends ey{constructor(t,e,i,s,r){super(t,e,i,s,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&eT(s=s.trim())){let r=function t(e,i,s=1){q(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,n]=function(t){let e=eS.exec(t);if(!e)return[,];let[,i,s,r]=e;return[`--${null!=i?i:s}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return ex(t)?parseFloat(t):t}return eT(n)?t(n,i,s+1):n}(s,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!X.has(i)||2!==t.length)return;let[s,r]=t,n=eE(s),o=eE(r);if(n!==o)if(ea(n)&&ea(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||tj(s))&&i.push(e)}i.length&&function(t,e,i){let s,r=0;for(;r<t.length&&!s;){let e=t[r];"string"==typeof e&&!eo.has(e)&&t2(e).values.length&&(s=t[r]),r++}if(s&&i)for(let r of e)t[r]=en(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ec[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){var t;let{element:e,name:i,unresolvedKeyframes:s}=this;if(!e||!e.current)return;let r=e.getValue(i);r&&r.jump(this.measuredOrigin,!1);let n=s.length-1,o=s[n];s[n]=ec[i](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),(null==(t=this.removedTransforms)?void 0:t.length)&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let eD=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(t9.test(t)||"0"===t)&&!t.startsWith("url(")),eM=t=>null!==t;function eV(t,{repeat:e,repeatType:i="loop"},s){let r=t.filter(eM),n=e&&"loop"!==i&&e%2==1?0:r.length-1;return n&&void 0!==s?s:r[n]}class eR{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:r=0,repeatType:n="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=tn.now(),this.options={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:r,repeatType:n,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(ev(),eg()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=tn.now(),this.hasAttemptedResolve=!0;let{name:i,type:s,velocity:r,delay:n,onComplete:o,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,i,s){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let n=t[t.length-1],o=eD(r,e),a=eD(n,e);return q(o===a,`You are trying to animate ${e} from "${r}" to "${n}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${n} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||T(i))&&s)}(t,i,s,r))if(tv.current||!n){a&&a(eV(t,this.options,e)),o&&o(),this.resolveFinishedPromise();return}else this.options.duration=0;let u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}let eL=(t,e,i)=>t+(e-t)*i;function eF(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function eN(t,e){return i=>i>0?e:t}let eB=(t,e,i)=>{let s=t*t,r=i*(e*e-s)+s;return r<0?0:Math.sqrt(r)},eI=[tz,t$,tG],eO=t=>eI.find(e=>e.test(t));function eU(t){let e=eO(t);if(q(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tG&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let r=0,n=0,o=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,a=2*i-s;r=eF(a,s,t+1/3),n=eF(a,s,t),o=eF(a,s,t-1/3)}else r=n=o=i;return{red:Math.round(255*r),green:Math.round(255*n),blue:Math.round(255*o),alpha:s}}(i)),i}let e$=(t,e)=>{let i=eU(t),s=eU(e);if(!i||!s)return eN(t,e);let r={...i};return t=>(r.red=eB(i.red,s.red,t),r.green=eB(i.green,s.green,t),r.blue=eB(i.blue,s.blue,t),r.alpha=eL(i.alpha,s.alpha,t),t$.transform(r))},ez=(t,e)=>i=>e(t(i)),eW=(...t)=>t.reduce(ez),e_=new Set(["none","hidden"]);function eq(t,e){return i=>eL(t,e,i)}function eH(t){return"number"==typeof t?eq:"string"==typeof t?eT(t)?eN:tZ.test(t)?e$:eK:Array.isArray(t)?eY:"object"==typeof t?tZ.test(t)?e$:eX:eN}function eY(t,e){let i=[...t],s=i.length,r=t.map((t,i)=>eH(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=r[e](t);return i}}function eX(t,e){let i={...t,...e},s={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(s[r]=eH(t[r])(t[r],e[r]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let eK=(t,e)=>{let i=t9.createTransformer(e),s=t2(t),r=t2(e);return s.indexes.var.length===r.indexes.var.length&&s.indexes.color.length===r.indexes.color.length&&s.indexes.number.length>=r.indexes.number.length?e_.has(t)&&!r.values.length||e_.has(e)&&!s.values.length?function(t,e){return e_.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):eW(eY(function(t,e){var i;let s=[],r={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let o=e.types[n],a=t.indexes[o][r[o]],l=null!=(i=t.values[a])?i:0;s[n]=l,r[o]++}return s}(s,r),r.values),i):(q(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eN(t,e))};function eG(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?eL(t,e,i):eH(t)(t,e)}function eZ(t,e,i){var s,r;let n=Math.max(e-5,0);return s=i-t(n),(r=e-n)?1e3/r*s:0}let eJ={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eQ(t,e){return t*Math.sqrt(1-e*e)}let e0=["duration","bounce"],e1=["stiffness","damping","mass"];function e2(t,e){return e.some(e=>void 0!==t[e])}function e3(t=eJ.visualDuration,e=eJ.bounce){let i,s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:n}=s,o=s.keyframes[0],a=s.keyframes[s.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:eJ.velocity,stiffness:eJ.stiffness,damping:eJ.damping,mass:eJ.mass,isResolvedFromDuration:!1,...t};if(!e2(t,e1)&&e2(t,e0))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,r=2*tD(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:eJ.mass,stiffness:s,damping:r}}else{let i=function({duration:t=eJ.duration,bounce:e=eJ.bounce,velocity:i=eJ.velocity,mass:s=eJ.mass}){let r,n;q(t<=W(eJ.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=tD(eJ.minDamping,eJ.maxDamping,o),t=tD(eJ.minDuration,eJ.maxDuration,_(t)),o<1?(r=e=>{let s=e*o,r=s*t;return .001-(s-i)/eQ(e,o)*Math.exp(-r)},n=e=>{let s=e*o*t,n=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-s),l=eQ(Math.pow(e,2),o);return(s*i+i-n)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),n=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(r,n,5/t);if(t=W(t),isNaN(a))return{stiffness:eJ.stiffness,damping:eJ.damping,duration:t};{let e=Math.pow(a,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:eJ.mass}).isResolvedFromDuration=!0}return e}({...s,velocity:-_(s.velocity||0)}),f=p||0,g=h/(2*Math.sqrt(u*d)),v=a-o,y=_(Math.sqrt(u/d)),x=5>Math.abs(v);if(r||(r=x?eJ.restSpeed.granular:eJ.restSpeed.default),n||(n=x?eJ.restDelta.granular:eJ.restDelta.default),g<1){let t=eQ(y,g);i=e=>a-Math.exp(-g*y*e)*((f+g*y*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-y*t)*(v+(f+y*v)*t);else{let t=y*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*y*e),s=Math.min(t*e,300);return a-i*((f+g*y*v)*Math.sinh(s)+t*v*Math.cosh(s))/t}}let b={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let s=0;g<1&&(s=0===t?W(f):eZ(i,t,e));let o=Math.abs(a-e)<=n;l.done=Math.abs(s)<=r&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(P(b),2e4),e=j(e=>b.next(t*e).value,t,30);return t+"ms "+e}};return b}function e5({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:r=10,bounceStiffness:n=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,v=i*e,y=p+v,x=void 0===o?y:o(y);x!==y&&(v=x-p);let b=t=>-v*Math.exp(-t/s),w=t=>x+b(t),P=t=>{let e=b(t),i=w(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},T=t=>{f(m.value)&&(d=t,c=e3({keyframes:[m.value,g(m.value)],velocity:eZ(w,t,m.value),damping:r,stiffness:n,restDelta:u,restSpeed:h}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,P(t),T(t)),void 0!==d&&t>=d)?c.next(t-d):(e||P(t),m)}}}let e4=tx(.42,0,1,1),e9=tx(0,0,.58,1),e6=tx(.42,0,.58,1),e8=t=>Array.isArray(t)&&"number"!=typeof t[0],e7={linear:q,easeIn:e4,easeInOut:e6,easeOut:e9,circIn:tC,circInOut:tE,circOut:tk,backIn:tT,backInOut:tA,backOut:tP,anticipate:tS},it=t=>{if(S(t)){q(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,r]=t;return tx(e,i,s,r)}return"string"==typeof t?(q(void 0!==e7[t],`Invalid easing type '${t}'`),e7[t]):t};function ie({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){var r;let n=e8(s)?s.map(it):it(s),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:s,mixer:r}={}){let n=t.length;if(q(n===e.length,"Both input and output ranges must be the same length"),1===n)return()=>e[0];if(2===n&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[n-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let s=[],r=i||eG,n=t.length-1;for(let i=0;i<n;i++){let n=r(t[i],t[i+1]);e&&(n=eW(Array.isArray(e)?e[i]||q:e,n)),s.push(n)}return s}(e,s,r),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let s=0;if(l>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let r=E(t[s],t[s+1],i);return a[s](r)};return i?e=>u(tD(t[0],t[n-1],e)):u}((r=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let r=E(0,e,s);t.push(eL(i,1,r))}}(e,t.length-1),e}(e),r.map(e=>e*t)),e,{ease:Array.isArray(n)?n:e.map(()=>n||e6).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let ii=t=>{let e=({timestamp:e})=>t(e);return{start:()=>tt.update(e,!0),stop:()=>te(e),now:()=>ti.isProcessing?ti.timestamp:tn.now()}},is={decay:e5,inertia:e5,tween:ie,keyframes:ie,spring:e3},ir=t=>t/100;class io extends eR{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:s,keyframes:r}=this.options,n=(null==s?void 0:s.KeyframeResolver)||ey;this.resolver=new n(r,(t,e)=>this.onKeyframesResolved(t,e),e,i,s),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i,{type:s="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:o,velocity:a=0}=this.options,l=T(s)?s:is[s]||ie;l!==ie&&"number"!=typeof t[0]&&(e=eW(ir,eG(t[0],t[1])),t=[0,100]);let u=l({...this.options,keyframes:t});"mirror"===o&&(i=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=P(u));let{calculatedDuration:h}=u,d=h+n;return{generator:u,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:h,resolvedDuration:d,totalDuration:d*(r+1)-n}}onPostResolved(){let{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:s,generator:r,mirroredGenerator:n,mapPercentToKeyframes:o,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:h}=i;if(null===this.startTime)return r.next(0);let{delay:d,repeat:c,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let g=this.currentTime-d*(this.speed>=0?1:-1),v=this.speed>=0?g<0:g>u;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,x=r;if(c){let t=Math.min(this.currentTime,u)/h,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/h)):"mirror"===p&&(x=n)),y=tD(0,1,i)*h}let b=v?{done:!1,value:a[0]}:x.next(y);o&&(b.value=o(b.value));let{done:w}=b;v||null===l||(w=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return P&&void 0!==s&&(b.value=eV(a,this.options,s)),f&&f(b.value),P&&this.finish(),b}get duration(){let{resolved:t}=this;return t?_(t.calculatedDuration):0}get time(){return _(this.currentTime)}set time(t){t=W(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=_(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=ii,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let s=this.driver.now();null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=s):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!=(t=this.currentTime)?t:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}let ia=new Set(["opacity","clipPath","filter","transform"]),il=v(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),iu={anticipate:tS,backInOut:tA,circInOut:tE};class ih extends eR{constructor(t){super(t);let{name:e,motionValue:i,element:s,keyframes:r}=this.options;this.resolver=new ej(r,(t,e)=>this.onKeyframesResolved(t,e),e,i,s),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:s=300,times:r,ease:n,type:o,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof n&&k()&&n in iu&&(n=iu[n]),T((i=this.options).type)||"spring"===i.type||!function t(e){return!!("function"==typeof e&&k()||!e||"string"==typeof e&&(e in M||k())||S(e)||Array.isArray(e)&&e.every(t))}(i.ease)){let{onComplete:e,onUpdate:i,motionValue:a,element:l,...u}=this.options,h=function(t,e){let i=new io({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),s={done:!1,value:t[0]},r=[],n=0;for(;!s.done&&n<2e4;)r.push((s=i.sample(n)).value),n+=10;return{times:void 0,keyframes:r,duration:n-10,ease:"linear"}}(t,u);1===(t=h.keyframes).length&&(t[1]=t[0]),s=h.duration,r=h.times,n=h.ease,o="keyframes"}let h=function(t,e,i,{delay:s=0,duration:r=300,repeat:n=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){let u={[e]:i};l&&(u.offset=l);let h=function t(e,i){if(e)return"function"==typeof e&&k()?j(e,i):S(e)?D(e):Array.isArray(e)?e.map(e=>t(e,i)||M.easeOut):M[e]}(a,r);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:s,duration:r,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:n+1,direction:"reverse"===o?"alternate":"normal"})}(a.owner.current,l,t,{...this.options,duration:s,times:r,ease:n});return h.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(A(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{let{onComplete:i}=this.options;a.set(eV(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:s,times:r,type:o,ease:n,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return _(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return _(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=W(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return q;let{animation:i}=e;A(i,t)}else this.pendingTimeline=t;return q}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:s,type:r,ease:n,times:o}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,h=new io({...u,keyframes:i,duration:s,type:r,ease:n,times:o,isGenerator:!0}),d=W(this.time);t.setWithVelocity(h.sample(d-10).value,h.sample(d).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:r,damping:n,type:o}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return il()&&i&&ia.has(i)&&!a&&!l&&!s&&"mirror"!==r&&0!==n&&"inertia"!==o}}let id={type:"spring",stiffness:500,damping:25,restSpeed:10},ic=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),ip={type:"keyframes",duration:.8},im={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ig=(t,{keyframes:e})=>e.length>2?ip:Y.has(t)?t.startsWith("scale")?ic(e[1]):id:im,iv=(t,e,i,s={},r,n)=>o=>{let a=w(s,t)||{},l=a.delay||s.delay||0,{elapsed:u=0}=s;u-=W(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:n?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:r,repeat:n,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&(h={...h,...ig(t,h)}),h.duration&&(h.duration=W(h.duration)),h.repeatDelay&&(h.repeatDelay=W(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(d=!0)),(tv.current||Z.skipAnimations)&&(d=!0,h.duration=0,h.delay=0),d&&!n&&void 0!==e.get()){let t=eV(h.keyframes,a);if(void 0!==t)return tt.update(()=>{h.onUpdate(t),h.onComplete()}),new b([])}return!n&&ih.supports(h)?new ih(h):new io(h)};function iy(t,e,{delay:i=0,transitionOverride:s,type:r}={}){var n;let{transition:o=t.getDefaultTransition(),transitionEnd:a,...l}=e;s&&(o=s);let u=[],h=r&&t.animationState&&t.animationState.getState()[r];for(let e in l){let s=t.getValue(e,null!=(n=t.latestValues[e])?n:null),r=l[e];if(void 0===r||h&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(h,e))continue;let a={delay:i,...w(o||{},e)},d=!1;if(window.MotionHandoffAnimation){let i=t.props[tg];if(i){let t=window.MotionHandoffAnimation(i,e,tt);null!==t&&(a.startTime=t,d=!0)}}tm(t,e),s.start(iv(e,s,r,t.shouldReduceMotion&&X.has(e)?{type:!1}:a,t,d));let c=s.animation;c&&u.push(c)}return a&&Promise.all(u).then(()=>{tt.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:s={},...r}=m(t,e)||{};for(let e in r={...r,...i}){let i=G(r[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,tc(i))}}(t,a)})}),u}function ix(t,e,i={}){var s;let r=m(t,e,"exit"===i.type?null==(s=t.presenceContext)?void 0:s.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(n=i.transitionOverride);let o=r?()=>Promise.all(iy(t,r,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:r=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,i=0,s=0,r=1,n){let o=[],a=(t.variantChildren.size-1)*s,l=1===r?(t=0)=>t*s:(t=0)=>a-t*s;return Array.from(t.variantChildren).sort(ib).forEach((t,s)=>{t.notify("AnimationStart",e),o.push(ix(t,e,{...n,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,r+s,o,a,i)}:()=>Promise.resolve(),{when:l}=n;if(!l)return Promise.all([o(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[o,a]:[a,o];return t().then(()=>e())}}function ib(t,e){return t.sortNodePosition(e)}let iw=g.length,iP=[...f].reverse(),iT=f.length;function iA(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iS(){return{animate:iA(!0),whileInView:iA(),whileHover:iA(),whileTap:iA(),whileDrag:iA(),whileFocus:iA(),exit:iA()}}class iC{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ik extends iC{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>ix(t,e,i)));else if("string"==typeof e)s=ix(t,e,i);else{let r="function"==typeof e?m(t,e,i.custom):e;s=Promise.all(iy(t,r,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iS(),s=!0,r=e=>(i,s)=>{var r;let n=m(t,s,"exit"===e?null==(r=t.presenceContext)?void 0:r.custom:void 0);if(n){let{transition:t,transitionEnd:e,...s}=n;i={...i,...s,...e}}return i};function n(n){let{props:o}=t,a=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<iw;t++){let s=g[t],r=e.props[s];(d(r)||!1===r)&&(i[s]=r)}return i}(t.parent)||{},c=[],p=new Set,m={},f=1/0;for(let e=0;e<iT;e++){var v,y;let g=iP[e],x=i[g],b=void 0!==o[g]?o[g]:a[g],w=d(b),P=g===n?x.isActive:null;!1===P&&(f=e);let T=b===a[g]&&b!==o[g]&&w;if(T&&s&&t.manuallyAnimateOnMount&&(T=!1),x.protectedKeys={...m},!x.isActive&&null===P||!b&&!x.prevProp||l(b)||"boolean"==typeof b)continue;let A=(v=x.prevProp,"string"==typeof(y=b)?y!==v:!!Array.isArray(y)&&!h(y,v)),S=A||g===n&&x.isActive&&!T&&w||e>f&&w,C=!1,k=Array.isArray(b)?b:[b],E=k.reduce(r(g),{});!1===P&&(E={});let{prevResolvedValues:j={}}=x,D={...j,...E},M=e=>{S=!0,p.has(e)&&(C=!0,p.delete(e)),x.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in D){let e=E[t],i=j[t];if(m.hasOwnProperty(t))continue;let s=!1;(u(e)&&u(i)?h(e,i):e===i)?void 0!==e&&p.has(t)?M(t):x.protectedKeys[t]=!0:null!=e?M(t):p.add(t)}x.prevProp=b,x.prevResolvedValues=E,x.isActive&&(m={...m,...E}),s&&t.blockInitialAnimation&&(S=!1);let V=!(T&&A)||C;S&&V&&c.push(...k.map(t=>({animation:t,options:{type:g}})))}if(p.size){let e={};p.forEach(i=>{let s=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=null!=s?s:null}),c.push({animation:e})}let x=!!c.length;return s&&(!1===o.initial||o.initial===o.animate)&&!t.manuallyAnimateOnMount&&(x=!1),s=!1,x?e(c):Promise.resolve()}return{animateChanges:n,setActive:function(e,s){var r;if(i[e].isActive===s)return Promise.resolve();null==(r=t.variantChildren)||r.forEach(t=>{var i;return null==(i=t.animationState)?void 0:i.setActive(e,s)}),i[e].isActive=s;let o=n(e);for(let t in i)i[t].protectedKeys={};return o},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iS(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();l(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null==(t=this.unmountControls)||t.call(this)}}let iE=0;class ij extends iC{constructor(){super(...arguments),this.id=iE++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}function iD(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}function iM(t){return{point:{x:t.pageX,y:t.pageY}}}let iV=t=>e=>N(e)&&t(e,iM(e));function iR(t,e,i,s){return iD(t,e,iV(i),s)}let iL=(t,e)=>Math.abs(t-e);class iF{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iI(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iL(t.x,e.x)**2+iL(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:r}=ti;this.history.push({...s,timestamp:r});let{onStart:n,onMove:o}=this.handlers;e||(n&&n(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iN(e,this.transformPagePoint),tt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let n=iI("pointercancel"===t.type?this.lastMoveEventInfo:iN(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,n),s&&s(t,n)},!N(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let n=iN(iM(t),this.transformPagePoint),{point:o}=n,{timestamp:a}=ti;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iI(n,this.history)),this.removeListeners=eW(iR(this.contextWindow,"pointermove",this.handlePointerMove),iR(this.contextWindow,"pointerup",this.handlePointerUp),iR(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),te(this.updatePoint)}}function iN(t,e){return e?{point:e(t.point)}:t}function iB(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iI({point:t},e){return{point:t,delta:iB(t,iO(e)),offset:iB(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,r=iO(t);for(;i>=0&&(s=t[i],!(r.timestamp-s.timestamp>W(.1)));)i--;if(!s)return{x:0,y:0};let n=_(r.timestamp-s.timestamp);if(0===n)return{x:0,y:0};let o={x:(r.x-s.x)/n,y:(r.y-s.y)/n};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iO(t){return t[t.length-1]}function iU(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function i$(t){return t.max-t.min}function iz(t,e,i,s=.5){t.origin=s,t.originPoint=eL(e.min,e.max,t.origin),t.scale=i$(i)/i$(e),t.translate=eL(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iW(t,e,i,s){iz(t.x,e.x,i.x,s?s.originX:void 0),iz(t.y,e.y,i.y,s?s.originY:void 0)}function i_(t,e,i){t.min=i.min+e.min,t.max=t.min+i$(e)}function iq(t,e,i){t.min=e.min-i.min,t.max=t.min+i$(e)}function iH(t,e,i){iq(t.x,e.x,i.x),iq(t.y,e.y,i.y)}function iY(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iX(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function iK(t,e,i){return{min:iG(t,e),max:iG(t,i)}}function iG(t,e){return"number"==typeof t?t:t[e]||0}let iZ=()=>({translate:0,scale:1,origin:0,originPoint:0}),iJ=()=>({x:iZ(),y:iZ()}),iQ=()=>({min:0,max:0}),i0=()=>({x:iQ(),y:iQ()});function i1(t){return[t("x"),t("y")]}function i2({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function i3(t){return void 0===t||1===t}function i5({scale:t,scaleX:e,scaleY:i}){return!i3(t)||!i3(e)||!i3(i)}function i4(t){return i5(t)||i9(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function i9(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function i6(t,e,i,s,r){return void 0!==r&&(t=s+r*(t-s)),s+i*(t-s)+e}function i8(t,e=0,i=1,s,r){t.min=i6(t.min,e,i,s,r),t.max=i6(t.max,e,i,s,r)}function i7(t,{x:e,y:i}){i8(t.x,e.translate,e.scale,e.originPoint),i8(t.y,i.translate,i.scale,i.originPoint)}function st(t,e){t.min=t.min+e,t.max=t.max+e}function se(t,e,i,s,r=.5){let n=eL(t.min,t.max,r);i8(t,e,i,n,s)}function si(t,e){se(t.x,e.x,e.scaleX,e.scale,e.originX),se(t.y,e.y,e.scaleY,e.scale,e.originY)}function ss(t,e){return i2(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let sr=({current:t})=>t?t.ownerDocument.defaultView:null,sn=new WeakMap;class so{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=i0(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new iF(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iM(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:r}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(V[t])return null;else return V[t]=!0,()=>{V[t]=!1};return V.x||V.y?null:(V.x=V.y=!0,()=>{V.x=V.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),i1(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tq.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=i$(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&tt.postRender(()=>r(t,e)),tm(this.visualElement,"transform");let{animationState:n}=this.visualElement;n&&n.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:r,onDrag:n}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),n&&n(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>i1(t=>{var e;return"paused"===this.getAnimationState(t)&&(null==(e=this.getAxisMotionValue(t).animation)?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:sr(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:r}=this.getProps();r&&tt.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!sa(t,s,this.currentDirection))return;let r=this.getAxisMotionValue(t),n=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(n=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?eL(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?eL(i,t,s.max):Math.min(t,i)),t}(n,this.constraints[t],this.elastic[t])),r.set(n)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(t=this.visualElement.projection)?void 0:t.layout,r=this.constraints;e&&iU(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=function(t,{top:e,left:i,bottom:s,right:r}){return{x:iY(t.x,i,r),y:iY(t.y,e,s)}}(s.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:iK(t,"left","right"),y:iK(t,"top","bottom")}}(i),r!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&i1(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(s.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iU(e))return!1;let s=e.current;q(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let n=function(t,e,i){let s=ss(t,i),{scroll:r}=e;return r&&(st(s.x,r.offset.x),st(s.y,r.offset.y)),s}(s,r.root,this.visualElement.getTransformPagePoint()),o=(t=r.layout.layoutBox,{x:iX(t.x,n.x),y:iX(t.y,n.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=i2(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:r,dragSnapToOrigin:n,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(i1(o=>{if(!sa(o,e,this.currentDirection))return;let l=a&&a[o]||{};n&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return tm(this.visualElement,t),i.start(iv(t,i,0,e,this.visualElement,!1))}stopAnimation(){i1(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){i1(t=>{var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.pause()})}getAnimationState(t){var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){i1(e=>{let{drag:i}=this.getProps();if(!sa(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,r=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:n}=s.layout.layoutBox[e];r.set(t[e]-eL(i,n,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iU(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};i1(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=i$(t),r=i$(e);return r>s?i=E(e.min,e.max-s,t.min):s>r&&(i=E(t.min,t.max-r,e.min)),tD(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),i1(e=>{if(!sa(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:n}=this.constraints[e];i.set(eL(r,n,s[e]))})}addListeners(){if(!this.visualElement.current)return;sn.set(this.visualElement,this);let t=iR(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iU(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),tt.read(e);let r=iD(window,"resize",()=>this.scalePositionWithinConstraints()),n=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(i1(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),s(),n&&n()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:r=!1,dragElastic:n=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:r,dragElastic:n,dragMomentum:o}}}function sa(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class sl extends iC{constructor(t){super(t),this.removeGroupControls=q,this.removeListeners=q,this.controls=new so(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||q}unmount(){this.removeGroupControls(),this.removeListeners()}}let su=t=>(e,i)=>{t&&tt.postRender(()=>t(e,i))};class sh extends iC{constructor(){super(...arguments),this.removePointerDownListener=q}onPointerDown(t){this.session=new iF(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:sr(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:su(t),onStart:su(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&tt.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=iR(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let sd=(0,a.createContext)(null),sc=(0,a.createContext)({}),sp=(0,a.createContext)({}),sm={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function sf(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let sg={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tH.test(t))return t;else t=parseFloat(t);let i=sf(t,e.target.x),s=sf(t,e.target.y);return`${i}% ${s}%`}},sv={},{schedule:sy,cancel:sx}=Q(queueMicrotask,!1);class sb extends a.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:r}=t;Object.assign(sv,sP),r&&(e.group&&e.group.add(r),i&&i.register&&s&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),sm.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:r}=this.props,n=i.projection;return n&&(n.isPresent=r,s||t.layoutDependency!==e||void 0===e?n.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?n.promote():n.relegate()||tt.postRender(()=>{let t=n.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),sy.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function sw(t){let[e,i]=function(t=!0){let e=(0,a.useContext)(sd);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:s,register:r}=e,n=(0,a.useId)();(0,a.useEffect)(()=>{t&&r(n)},[t]);let o=(0,a.useCallback)(()=>t&&s&&s(n),[n,s,t]);return!i&&s?[!1,o]:[!0]}(),s=(0,a.useContext)(sc);return(0,o.jsx)(sb,{...t,layoutGroup:s,switchLayoutGroup:(0,a.useContext)(sp),isPresent:e,safeToRemove:i})}let sP={borderRadius:{...sg,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:sg,borderTopRightRadius:sg,borderBottomLeftRadius:sg,borderBottomRightRadius:sg,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=t9.parse(t);if(s.length>5)return t;let r=t9.createTransformer(t),n=+("number"!=typeof s[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;s[0+n]/=o,s[1+n]/=a;let l=eL(o,a,.5);return"number"==typeof s[2+n]&&(s[2+n]/=l),"number"==typeof s[3+n]&&(s[3+n]/=l),r(s)}}},sT=(t,e)=>t.depth-e.depth;class sA{constructor(){this.children=[],this.isDirty=!1}add(t){to(this.children,t),this.isDirty=!0}remove(t){ta(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(sT),this.isDirty=!1,this.children.forEach(t)}}function sS(t){let e=tp(t)?t.get():t;return K(e)?e.toValue():e}let sC=["TopLeft","TopRight","BottomLeft","BottomRight"],sk=sC.length,sE=t=>"string"==typeof t?parseFloat(t):t,sj=t=>"number"==typeof t||tH.test(t);function sD(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let sM=sR(0,.5,tk),sV=sR(.5,.95,q);function sR(t,e,i){return s=>s<t?0:s>e?1:i(E(t,e,s))}function sL(t,e){t.min=e.min,t.max=e.max}function sF(t,e){sL(t.x,e.x),sL(t.y,e.y)}function sN(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function sB(t,e,i,s,r){return t-=e,t=s+1/i*(t-s),void 0!==r&&(t=s+1/r*(t-s)),t}function sI(t,e,[i,s,r],n,o){!function(t,e=0,i=1,s=.5,r,n=t,o=t){if(tq.test(e)&&(e=parseFloat(e),e=eL(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=eL(n.min,n.max,s);t===n&&(a-=e),t.min=sB(t.min,e,i,a,r),t.max=sB(t.max,e,i,a,r)}(t,e[i],e[s],e[r],e.scale,n,o)}let sO=["x","scaleX","originX"],sU=["y","scaleY","originY"];function s$(t,e,i,s){sI(t.x,e,sO,i?i.x:void 0,s?s.x:void 0),sI(t.y,e,sU,i?i.y:void 0,s?s.y:void 0)}function sz(t){return 0===t.translate&&1===t.scale}function sW(t){return sz(t.x)&&sz(t.y)}function s_(t,e){return t.min===e.min&&t.max===e.max}function sq(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function sH(t,e){return sq(t.x,e.x)&&sq(t.y,e.y)}function sY(t){return i$(t.x)/i$(t.y)}function sX(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class sK{constructor(){this.members=[]}add(t){to(this.members,t),t.scheduleRender()}remove(t){if(ta(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let sG={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},sZ="undefined"!=typeof window&&void 0!==window.MotionDebug,sJ=["","X","Y","Z"],sQ={visibility:"hidden"},s0=0;function s1(t,e,i,s){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),s&&(s[t]=0))}function s2({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:r}){return class{constructor(t={},i=null==e?void 0:e()){this.id=s0++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,sZ&&(sG.totalNodes=sG.resolvedTargetDeltas=sG.recalculatedProjection=0),this.nodes.forEach(s4),this.nodes.forEach(ri),this.nodes.forEach(rs),this.nodes.forEach(s9),sZ&&window.MotionDebug.record(sG)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new sA)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new tl),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:s,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(r||s)&&(this.isLayoutDirty=!0),t){let i,s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=tn.now(),s=({timestamp:r})=>{let n=r-i;n>=250&&(te(s),t(n-e))};return tt.read(s,!0),()=>te(s)}(s,250),sm.hasAnimatedSinceResize&&(sm.hasAnimatedSinceResize=!1,this.nodes.forEach(re))})}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&n&&(s||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||n.getDefaultTransition()||ru,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=n.getProps(),l=!this.targetLayout||!sH(this.targetLayout,s)||i,u=!e&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...w(r,"layout"),onPlay:o,onComplete:a};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||re(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,te(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rr),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[tg];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",tt,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(s8);return}this.isUpdating||this.nodes.forEach(s7),this.isUpdating=!1,this.nodes.forEach(rt),this.nodes.forEach(s3),this.nodes.forEach(s5),this.clearAllSnapshots();let t=tn.now();ti.delta=tD(0,1e3/60,t-ti.timestamp),ti.timestamp=t,ti.isProcessing=!0,ts.update.process(ti),ts.preRender.process(ti),ts.render.process(ti),ti.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,sy.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(s6),this.sharedNodes.forEach(rn)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=i0(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sW(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,n=s!==this.prevTransformTemplateValue;t&&(e||i4(this.latestValues)||n)&&(r(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),rc((e=s).x),rc(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return i0();let i=e.measureViewportBox();if(!((null==(t=this.scroll)?void 0:t.wasRoot)||this.path.some(rm))){let{scroll:t}=this.root;t&&(st(i.x,t.offset.x),st(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=i0();if(sF(i,t),null==(e=this.scroll)?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let s=this.path[e],{scroll:r,options:n}=s;s!==this.root&&r&&n.layoutScroll&&(r.wasRoot&&sF(i,t),st(i.x,r.offset.x),st(i.y,r.offset.y))}return i}applyTransform(t,e=!1){let i=i0();sF(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&si(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),i4(s.latestValues)&&si(i,s.latestValues)}return i4(this.latestValues)&&si(i,this.latestValues),i}removeTransform(t){let e=i0();sF(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!i4(i.latestValues))continue;i5(i.latestValues)&&i.updateSnapshot();let s=i0();sF(s,i.measurePageBox()),s$(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return i4(this.latestValues)&&s$(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ti.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,i,s,r;let n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==n;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=ti.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=i0(),this.relativeTargetOrigin=i0(),iH(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sF(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=i0(),this.targetWithTransforms=i0()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,s=this.relativeTarget,r=this.relativeParent.target,i_(i.x,s.x,r.x),i_(i.y,s.y,r.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sF(this.target,this.layout.layoutBox),i7(this.target,this.targetDelta)):sF(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=i0(),this.relativeTargetOrigin=i0(),iH(this.relativeTargetOrigin,this.target,t.target),sF(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}sZ&&sG.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||i5(this.parent.latestValues)||i9(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,s=!0;if((this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty))&&(s=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(s=!1),this.resolvedRelativeTargetAt===ti.timestamp&&(s=!1),s)return;let{layout:r,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||n))return;sF(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,s=!1){let r,n,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){n=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&si(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),n&&(e.x*=n.x.scale,e.y*=n.y.scale,i7(t,n)),s&&i4(r.latestValues)&&si(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=i0());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sN(this.prevProjectionDelta.x,this.projectionDelta.x),sN(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iW(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&sX(this.projectionDelta.x,this.prevProjectionDelta.x)&&sX(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),sZ&&sG.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null==(e=this.options.visualElement)||e.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iJ(),this.projectionDelta=iJ(),this.projectionDeltaWithTransform=iJ()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,r=s?s.latestValues:{},n={...this.latestValues},o=iJ();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=i0(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(rl));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(ro(o.x,t.x,s),ro(o.y,t.y,s),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,g;iH(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=s,ra(p.x,m.x,f.x,g),ra(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,c=i,s_(u.x,c.x)&&s_(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=i0()),sF(i,this.relativeTarget)}l&&(this.animationValues=n,function(t,e,i,s,r,n){r?(t.opacity=eL(0,void 0!==i.opacity?i.opacity:1,sM(s)),t.opacityExit=eL(void 0!==e.opacity?e.opacity:1,0,sV(s))):n&&(t.opacity=eL(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,s));for(let r=0;r<sk;r++){let n=`border${sC[r]}Radius`,o=sD(e,n),a=sD(i,n);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||sj(o)===sj(a)?(t[n]=Math.max(eL(sE(o),sE(a),s),0),(tq.test(a)||tq.test(o))&&(t[n]+="%")):t[n]=a)}(e.rotate||i.rotate)&&(t.rotate=eL(e.rotate||0,i.rotate||0,s))}(n,r,this.latestValues,s,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(te(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tt.update(()=>{sm.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,i){let s=tp(0)?0:tc(t);return s.start(iv("",s,1e3,i)),s.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:r}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&rp(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||i0();let e=i$(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=i$(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}sF(e,i),si(e,r),iW(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new sK),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null==(t=this.getStack())?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null==(t=this.getStack())?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&s1("z",t,s,this.animationValues);for(let e=0;e<sJ.length;e++)s1(`rotate${sJ[e]}`,t,s,this.animationValues),s1(`skew${sJ[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return sQ;let s={visibility:""},r=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,s.opacity="",s.pointerEvents=sS(null==t?void 0:t.pointerEvents)||"",s.transform=r?r(this.latestValues,""):"none",s;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=sS(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!i4(this.latestValues)&&(e.transform=r?r({},""):"none",this.hasProjected=!1),e}let o=n.animationValues||n.latestValues;this.applyTransformsToTarget(),s.transform=function(t,e,i){let s="",r=t.x.translate/e.x,n=t.y.translate/e.y,o=(null==i?void 0:i.z)||0;if((r||n||o)&&(s=`translate3d(${r}px, ${n}px, ${o}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:n,skewX:o,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),r&&(s+=`rotateX(${r}deg) `),n&&(s+=`rotateY(${n}deg) `),o&&(s+=`skewX(${o}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),r&&(s.transform=r(o,s.transform));let{x:a,y:l}=this.projectionDelta;for(let t in s.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,n.animationValues?s.opacity=n===this?null!=(i=null!=(e=o.opacity)?e:this.latestValues.opacity)?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:s.opacity=n===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,sv){if(void 0===o[t])continue;let{correct:e,applyTo:i}=sv[t],r="none"===s.transform?o[t]:e(o[t],n);if(i){let t=i.length;for(let e=0;e<t;e++)s[i[e]]=r}else s[t]=r}return this.options.layoutId&&(s.pointerEvents=n===this?sS(null==t?void 0:t.pointerEvents)||"":"none"),s}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null==(e=t.currentAnimation)?void 0:e.stop()}),this.root.nodes.forEach(s8),this.root.sharedNodes.clear()}}}function s3(t){t.updateLayout()}function s5(t){var e;let i=(null==(e=t.resumeFrom)?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:s}=t.layout,{animationType:r}=t.options,n=i.source!==t.layout.source;"size"===r?i1(t=>{let s=n?i.measuredBox[t]:i.layoutBox[t],r=i$(s);s.min=e[t].min,s.max=s.min+r}):rp(r,i.layoutBox,e)&&i1(s=>{let r=n?i.measuredBox[s]:i.layoutBox[s],o=i$(e[s]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+o)});let o=iJ();iW(o,e,i.layoutBox);let a=iJ();n?iW(a,t.applyTransform(s,!0),i.measuredBox):iW(a,e,i.layoutBox);let l=!sW(o),u=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:r,layout:n}=s;if(r&&n){let o=i0();iH(o,i.layoutBox,r.layoutBox);let a=i0();iH(a,e,n.layoutBox),sH(o,a)||(u=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function s4(t){sZ&&sG.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function s9(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function s6(t){t.clearSnapshot()}function s8(t){t.clearMeasurements()}function s7(t){t.isLayoutDirty=!1}function rt(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function re(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ri(t){t.resolveTargetDelta()}function rs(t){t.calcProjection()}function rr(t){t.resetSkewAndRotation()}function rn(t){t.removeLeadSnapshot()}function ro(t,e,i){t.translate=eL(e.translate,0,i),t.scale=eL(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function ra(t,e,i,s){t.min=eL(e.min,i.min,s),t.max=eL(e.max,i.max,s)}function rl(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let ru={duration:.45,ease:[.4,0,.1,1]},rh=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rd=rh("applewebkit/")&&!rh("chrome/")?Math.round:q;function rc(t){t.min=rd(t.min),t.max=rd(t.max)}function rp(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(sY(e)-sY(i)))}function rm(t){var e;return t!==t.root&&(null==(e=t.scroll)?void 0:e.wasRoot)}let rf=s2({attachResizeListener:(t,e)=>iD(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rg={current:void 0},rv=s2({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rg.current){let t=new rf({});t.mount(window),t.setOptions({layoutScroll:!0}),rg.current=t}return rg.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ry(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=s["onHover"+i];r&&tt.postRender(()=>r(e,iM(e)))}class rx extends iC{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,r,n]=R(t,i),o=L(t=>{let{target:i}=t,s=e(t);if("function"!=typeof s||!i)return;let n=L(t=>{s(t),i.removeEventListener("pointerleave",n)});i.addEventListener("pointerleave",n,r)});return s.forEach(t=>{t.addEventListener("pointerenter",o,r)}),n}(t,t=>(ry(this.node,t,"Start"),t=>ry(this.node,t,"End"))))}unmount(){}}class rb extends iC{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eW(iD(this.node.current,"focus",()=>this.onFocus()),iD(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function rw(t,e,i){let{props:s}=t;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=s["onTap"+("End"===i?"":i)];r&&tt.postRender(()=>r(e,iM(e)))}class rP extends iC{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,r,n]=R(t,i),o=t=>{let s=t.currentTarget;if(!z(t)||I.has(s))return;I.add(s);let n=e(t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),z(t)&&I.has(s)&&(I.delete(s),"function"==typeof n&&n(t,{success:e}))},a=t=>{o(t,i.useGlobalTarget||F(s,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return s.forEach(t=>{B.has(t.tagName)||-1!==t.tabIndex||null!==t.getAttribute("tabindex")||(t.tabIndex=0),(i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),t.addEventListener("focus",t=>$(t,r),r)}),n}(t,t=>(rw(this.node,t,"Start"),(t,{success:e})=>rw(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rT=new WeakMap,rA=new WeakMap,rS=t=>{let e=rT.get(t.target);e&&e(t)},rC=t=>{t.forEach(rS)},rk={some:0,all:1};class rE extends iC{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:r}=t,n={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:rk[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;rA.has(i)||rA.set(i,{});let s=rA.get(i),r=JSON.stringify(e);return s[r]||(s[r]=new IntersectionObserver(rC,{root:t,...e})),s[r]}(e);return rT.set(t,i),s.observe(t),()=>{rT.delete(t),s.unobserve(t)}}(this.node.current,n,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),n=e?i:s;n&&n(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rj=(0,a.createContext)({strict:!1}),rD=(0,a.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),rM=(0,a.createContext)({});function rV(t){return l(t.animate)||g.some(e=>d(t[e]))}function rR(t){return!!(rV(t)||t.variants)}function rL(t){return Array.isArray(t)?t.join(" "):t}let rF="undefined"!=typeof window,rN={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rB={};for(let t in rN)rB[t]={isEnabled:e=>rN[t].some(t=>!!e[t])};let rI=Symbol.for("motionComponentSymbol"),rO=rF?a.useLayoutEffect:a.useEffect,rU=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r$(t){if("string"!=typeof t||t.includes("-"));else if(rU.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let rz=t=>(e,i)=>{let s=(0,a.useContext)(rM),r=(0,a.useContext)(sd),n=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:i},s,r,n){let o={latestValues:function(t,e,i,s){let r={},n=s(t,{});for(let t in n)r[t]=sS(n[t]);let{initial:o,animate:a}=t,u=rV(t),h=rR(t);e&&h&&!u&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let d=!!i&&!1===i.initial,c=(d=d||!1===o)?a:o;if(c&&"boolean"!=typeof c&&!l(c)){let e=Array.isArray(c)?c:[c];for(let i=0;i<e.length;i++){let s=p(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(s,r,n,t),renderState:e()};return i&&(o.onMount=t=>i({props:s,current:t,...o}),o.onUpdate=t=>i(t)),o})(t,e,s,r);return i?n():function(t){let e=(0,a.useRef)(null);return null===e.current&&(e.current=t()),e.current}(n)},rW=(t,e)=>e&&"number"==typeof t?e.transform(t):t,r_={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rq=H.length;function rH(t,e,i){let{style:s,vars:r,transformOrigin:n}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(Y.has(t)){o=!0;continue}if(ew(t)){r[t]=i;continue}{let e=rW(i,ei[t]);t.startsWith("origin")?(a=!0,n[t]=e):s[t]=e}}if(!e.transform&&(o||i?s.transform=function(t,e,i){let s="",r=!0;for(let n=0;n<rq;n++){let o=H[n],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=rW(a,ei[o]);if(!l){r=!1;let e=r_[o]||o;s+=`${e}(${t}) `}i&&(e[o]=t)}}return s=s.trim(),i?s=i(e,r?"":s):r&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=n;s.transformOrigin=`${t} ${e} ${i}`}}let rY={offset:"stroke-dashoffset",array:"stroke-dasharray"},rX={offset:"strokeDashoffset",array:"strokeDasharray"};function rK(t,e,i){return"string"==typeof t?t:tH.transform(e+i*t)}function rG(t,{attrX:e,attrY:i,attrScale:s,originX:r,originY:n,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,d){if(rH(t,u,d),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:p,dimensions:m}=t;c.transform&&(m&&(p.transform=c.transform),delete c.transform),m&&(void 0!==r||void 0!==n||p.transform)&&(p.transformOrigin=function(t,e,i){let s=rK(e,t.x,t.width),r=rK(i,t.y,t.height);return`${s} ${r}`}(m,void 0!==r?r:.5,void 0!==n?n:.5)),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==s&&(c.scale=s),void 0!==o&&function(t,e,i=1,s=0,r=!0){t.pathLength=1;let n=r?rY:rX;t[n.offset]=tH.transform(-s);let o=tH.transform(e),a=tH.transform(i);t[n.array]=`${o} ${a}`}(c,o,a,l,!1)}let rZ=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),rJ=()=>({...rZ(),attrs:{}}),rQ=t=>"string"==typeof t&&"svg"===t.toLowerCase();function r0(t,{style:e,vars:i},s,r){for(let n in Object.assign(t.style,e,r&&r.getProjectionStyles(s)),i)t.style.setProperty(n,i[n])}let r1=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function r2(t,e,i,s){for(let i in r0(t,e,void 0,s),e.attrs)t.setAttribute(r1.has(i)?i:tf(i),e.attrs[i])}function r3(t,{layout:e,layoutId:i}){return Y.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!sv[t]||"opacity"===t)}function r5(t,e,i){var s;let{style:r}=t,n={};for(let o in r)(tp(r[o])||e.style&&tp(e.style[o])||r3(o,t)||(null==(s=null==i?void 0:i.getValue(o))?void 0:s.liveStyle)!==void 0)&&(n[o]=r[o]);return n}function r4(t,e,i){let s=r5(t,e,i);for(let i in t)(tp(t[i])||tp(e[i]))&&(s[-1!==H.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let r9=["x","y","width","height","cx","cy","r"],r6={useVisualState:rz({scrapeMotionValuesFromProps:r4,createRenderState:rJ,onUpdate:({props:t,prevProps:e,current:i,renderState:s,latestValues:r})=>{if(!i)return;let n=!!t.drag;if(!n){for(let t in r)if(Y.has(t)){n=!0;break}}if(!n)return;let o=!e;if(e)for(let i=0;i<r9.length;i++){let s=r9[i];t[s]!==e[s]&&(o=!0)}o&&tt.read(()=>{!function(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}(i,s),tt.render(()=>{rG(s,r,rQ(i.tagName),t.transformTemplate),r2(i,s)})})}})},r8={useVisualState:rz({scrapeMotionValuesFromProps:r5,createRenderState:rZ})};function r7(t,e,i){for(let s in e)tp(e[s])||r3(s,i)||(t[s]=e[s])}let nt=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ne(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||nt.has(t)}let ni=t=>!ne(t);try{!function(t){t&&(ni=e=>e.startsWith("on")?!ne(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}let ns={current:null},nr={current:!1},nn=[...ek,tZ,t9],no=t=>nn.find(eC(t)),na=new WeakMap,nl=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class nu{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:r,visualState:n},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ey,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=tn.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,tt.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=n;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=rV(e),this.isVariantNode=rR(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==a[t]&&tp(e)&&e.set(a[t],!1)}}mount(t){this.current=t,na.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),nr.current||function(){if(nr.current=!0,rF)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ns.current=t.matches;t.addListener(e),e()}else ns.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ns.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in na.delete(this.current),this.projection&&this.projection.unmount(),te(this.notifyUpdate),te(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=Y.has(t),r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tt.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),n=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),n(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rB){let e=rB[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):i0()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<nl.length;e++){let i=nl[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let r=e[s],n=i[s];if(tp(r))t.addValue(s,r);else if(tp(n))t.addValue(s,tc(r,{owner:t}));else if(n!==r)if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(s);t.addValue(s,tc(void 0!==e?e:r,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=tc(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let s=void 0===this.latestValues[t]&&this.current?null!=(i=this.getBaseTargetFromProps(this.props,t))?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=s&&("string"==typeof s&&(ex(s)||tj(s))?s=parseFloat(s):!no(s)&&t9.test(e)&&(s=en(t,e)),this.setBaseTarget(t,tp(s)?s.get():s)),tp(s)?s.get():s}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let i,{initial:s}=this.props;if("string"==typeof s||"object"==typeof s){let r=p(this.props,s,null==(e=this.presenceContext)?void 0:e.custom);r&&(i=r[t])}if(s&&void 0!==i)return i;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||tp(r)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new tl),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class nh extends nu{constructor(){super(...arguments),this.KeyframeResolver=ej}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tp(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class nd extends nh{constructor(){super(...arguments),this.type="html",this.renderInstance=r0}readValueFromInstance(t,e){if(Y.has(e)){let t=er(e);return t&&t.default||0}{let i=window.getComputedStyle(t),s=(ew(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return ss(t,e)}build(t,e,i){rH(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return r5(t,e,i)}}class nc extends nh{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=i0}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(Y.has(e)){let t=er(e);return t&&t.default||0}return e=r1.has(e)?e:tf(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return r4(t,e,i)}build(t,e,i){rG(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,s){r2(t,e,i,s)}mount(t){this.isSVGTag=rQ(t.tagName),super.mount(t)}}let np=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((r={animation:{Feature:ik},exit:{Feature:ij},inView:{Feature:rE},tap:{Feature:rP},focus:{Feature:rb},hover:{Feature:rx},pan:{Feature:sh},drag:{Feature:sl,ProjectionNode:rv,MeasureLayout:sw},layout:{ProjectionNode:rv,MeasureLayout:sw}},n=(t,e)=>r$(t)?new nc(e):new nd(e,{allowProjection:t!==a.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:s,Component:r}){var n,l;function u(t,n){var l,u,h;let c,p={...(0,a.useContext)(rD),...t,layoutId:function({layoutId:t}){let e=(0,a.useContext)(sc).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:m}=p,f=function(t){let{initial:e,animate:i}=function(t,e){if(rV(t)){let{initial:e,animate:i}=t;return{initial:!1===e||d(e)?e:void 0,animate:d(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,a.useContext)(rM));return(0,a.useMemo)(()=>({initial:e,animate:i}),[rL(e),rL(i)])}(t),g=s(t,m);if(!m&&rF){u=0,h=0,(0,a.useContext)(rj).strict;let t=function(t){let{drag:e,layout:i}=rB;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(p);c=t.MeasureLayout,f.visualElement=function(t,e,i,s,r){var n,o;let{visualElement:l}=(0,a.useContext)(rM),u=(0,a.useContext)(rj),h=(0,a.useContext)(sd),d=(0,a.useContext)(rD).reducedMotion,c=(0,a.useRef)(null);s=s||u.renderer,!c.current&&s&&(c.current=s(t,{visualState:e,parent:l,props:i,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:d}));let p=c.current,m=(0,a.useContext)(sp);p&&!p.projection&&r&&("html"===p.type||"svg"===p.type)&&function(t,e,i,s){let{layoutId:r,layout:n,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:n,alwaysMeasureLayout:!!o||a&&iU(a),visualElement:t,animationType:"string"==typeof n?n:"both",initialPromotionConfig:s,layoutScroll:l,layoutRoot:u})}(c.current,i,r,m);let f=(0,a.useRef)(!1);(0,a.useInsertionEffect)(()=>{p&&f.current&&p.update(i,h)});let g=i[tg],v=(0,a.useRef)(!!g&&!(null==(n=window.MotionHandoffIsComplete)?void 0:n.call(window,g))&&(null==(o=window.MotionHasOptimisedAnimation)?void 0:o.call(window,g)));return rO(()=>{p&&(f.current=!0,window.MotionIsMounted=!0,p.updateFeatures(),sy.render(p.render),v.current&&p.animationState&&p.animationState.animateChanges())}),(0,a.useEffect)(()=>{p&&(!v.current&&p.animationState&&p.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var t;null==(t=window.MotionHandoffMarkAsComplete)||t.call(window,g)}),v.current=!1))}),p}(r,g,p,e,t.ProjectionNode)}return(0,o.jsxs)(rM.Provider,{value:f,children:[c&&f.visualElement?(0,o.jsx)(c,{visualElement:f.visualElement,...p}):null,i(r,t,(l=f.visualElement,(0,a.useCallback)(t=>{t&&g.onMount&&g.onMount(t),l&&(t?l.mount(t):l.unmount()),n&&("function"==typeof n?n(t):iU(n)&&(n.current=t))},[l])),g,m,f.visualElement)]})}t&&function(t){for(let e in t)rB[e]={...rB[e],...t[e]}}(t),u.displayName=`motion.${"string"==typeof r?r:`create(${null!=(l=null!=(n=r.displayName)?n:r.name)?l:""})`}`;let h=(0,a.forwardRef)(u);return h[rI]=r,h}({...r$(t)?r6:r8,preloadedFeatures:r,useRender:function(t=!1){return(e,i,s,{latestValues:r},n)=>{let o=(r$(e)?function(t,e,i,s){let r=(0,a.useMemo)(()=>{let i=rJ();return rG(i,e,rQ(s),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};r7(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return r7(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,a.useMemo)(()=>{let i=rZ();return rH(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,r,n,e),l=function(t,e,i){let s={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(ni(r)||!0===i&&ne(r)||!e&&!ne(r)||t.draggable&&r.startsWith("onDrag"))&&(s[r]=t[r]);return s}(i,"string"==typeof e,t),u=e!==a.Fragment?{...l,...o,ref:s}:{},{children:h}=i,d=(0,a.useMemo)(()=>tp(h)?h.get():h,[h]);return(0,a.createElement)(e,{...u,children:d})}}(e),createVisualElement:n,Component:t})})),nm=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),nf=(...t)=>t.filter((t,e,i)=>!!t&&i.indexOf(t)===e).join(" ");var ng={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let nv=(0,a.forwardRef)(({color:t="currentColor",size:e=24,strokeWidth:i=2,absoluteStrokeWidth:s,className:r="",children:n,iconNode:o,...l},u)=>(0,a.createElement)("svg",{ref:u,...ng,width:e,height:e,stroke:t,strokeWidth:s?24*Number(i)/Number(e):i,className:nf("lucide",r),...l},[...o.map(([t,e])=>(0,a.createElement)(t,e)),...Array.isArray(n)?n:[n]])),ny=(t,e)=>{let i=(0,a.forwardRef)(({className:i,...s},r)=>(0,a.createElement)(nv,{ref:r,iconNode:e,className:nf(`lucide-${nm(t)}`,i),...s}));return i.displayName=`${t}`,i},nx=ny("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),nb=ny("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),nw=ny("CircleCheckBig",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),nP=ny("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),nT=ny("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),nA=ny("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),nS=ny("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]),nC=ny("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);function nk(){let[t,e]=(0,a.useState)([]),[i,s]=(0,a.useState)(!0),[r,n]=(0,a.useState)(""),[l,u]=(0,a.useState)("all"),[h,d]=(0,a.useState)("all"),c=t.filter(t=>{let e=t.arabic_description.toLowerCase().includes(r.toLowerCase())||t.description.toLowerCase().includes(r.toLowerCase()),i="all"===l||t.status===l,s="all"===h||t.department===h;return e&&i&&s}),p=t=>{switch(t){case"pending":return(0,o.jsx)(nx,{className:"w-4 h-4 text-yellow-500"});case"in_progress":return(0,o.jsx)(nb,{className:"w-4 h-4 text-blue-500 animate-spin"});case"resolved":return(0,o.jsx)(nw,{className:"w-4 h-4 text-green-500"});case"closed":return(0,o.jsx)(nP,{className:"w-4 h-4 text-gray-500"});default:return(0,o.jsx)(nT,{className:"w-4 h-4 text-gray-400"})}},m=t=>{switch(t){case"pending":return"bg-yellow-100 text-yellow-800";case"in_progress":return"bg-blue-100 text-blue-800";case"resolved":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},f=t=>{switch(t){case"critical":return"bg-red-100 text-red-800";case"high":return"bg-orange-100 text-orange-800";case"medium":return"bg-yellow-100 text-yellow-800";case"low":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},g=[...new Set(t.map(t=>t.department).filter(Boolean))];return i?(0,o.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,o.jsx)(nb,{className:"w-8 h-8 animate-spin text-blue-600"})}):(0,o.jsxs)("div",{className:"max-w-7xl mx-auto p-6",children:[(0,o.jsxs)(np.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"mb-8",children:[(0,o.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"لوحة تحكم الحالات"}),(0,o.jsx)("p",{className:"text-gray-600",children:"إدارة ومتابعة جميع حالات الدعم الفني"})]}),(0,o.jsx)(np.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)(nA,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,o.jsx)("input",{type:"text",placeholder:"البحث في الحالات...",value:r,onChange:t=>n(t.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",dir:"rtl"})]}),(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)(nS,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,o.jsxs)("select",{value:l,onChange:t=>u(t.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none",children:[(0,o.jsx)("option",{value:"all",children:"جميع الحالات"}),(0,o.jsx)("option",{value:"pending",children:"في الانتظار"}),(0,o.jsx)("option",{value:"in_progress",children:"قيد المعالجة"}),(0,o.jsx)("option",{value:"resolved",children:"تم الحل"}),(0,o.jsx)("option",{value:"closed",children:"مغلقة"})]})]}),(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)(nC,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,o.jsxs)("select",{value:h,onChange:t=>d(t.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none",children:[(0,o.jsx)("option",{value:"all",children:"جميع الأقسام"}),g.map(t=>(0,o.jsx)("option",{value:t,children:t},t))]})]})]})}),(0,o.jsx)("div",{className:"grid gap-6",children:0===c.length?(0,o.jsxs)(np.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-12",children:[(0,o.jsx)(nT,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد حالات"}),(0,o.jsx)("p",{className:"text-gray-500",children:"لم يتم العثور على حالات تطابق المعايير المحددة"})]}):c.map((t,e)=>(0,o.jsxs)(np.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.05*e},className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:[(0,o.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,o.jsxs)("div",{className:"flex items-center gap-3",children:[p(t.status),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"font-medium text-gray-900",children:t.description||"وصف غير متوفر"}),(0,o.jsx)("p",{className:"text-sm text-gray-500",children:new Date(t.created_at).toLocaleDateString("ar-SA")})]})]}),(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${m(t.status)}`,children:t.status}),(0,o.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${f(t.priority)}`,children:t.priority})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("span",{className:"text-xs text-gray-500",children:"القسم"}),(0,o.jsx)("p",{className:"font-medium",children:t.department})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("span",{className:"text-xs text-gray-500",children:"نوع الحالة"}),(0,o.jsx)("p",{className:"font-medium",children:t.case_type})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("span",{className:"text-xs text-gray-500",children:"الفئة"}),(0,o.jsx)("p",{className:"font-medium",children:t.category})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("span",{className:"text-xs text-gray-500",children:"SLA"}),(0,o.jsx)("p",{className:"font-medium",children:t.sla})]})]}),(0,o.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[(0,o.jsx)("span",{className:"text-xs text-gray-500 block mb-1",children:"الوصف الأصلي (عربي)"}),(0,o.jsx)("p",{className:"text-sm text-gray-700",dir:"rtl",children:t.arabic_description})]}),t.english_translation&&(0,o.jsxs)("div",{className:"bg-blue-50 rounded-lg p-3",children:[(0,o.jsx)("span",{className:"text-xs text-blue-600 block mb-1",children:"الترجمة الإنجليزية"}),(0,o.jsx)("p",{className:"text-sm text-blue-800",children:t.english_translation})]})]},t.id))})]})}},8971:(t,e,i)=>{Promise.resolve().then(i.t.bind(i,6346,23)),Promise.resolve().then(i.t.bind(i,7924,23)),Promise.resolve().then(i.t.bind(i,5656,23)),Promise.resolve().then(i.t.bind(i,99,23)),Promise.resolve().then(i.t.bind(i,8243,23)),Promise.resolve().then(i.t.bind(i,8827,23)),Promise.resolve().then(i.t.bind(i,2763,23)),Promise.resolve().then(i.t.bind(i,7173,23))},9121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:t=>{"use strict";t.exports=require("url")}};var e=require("../../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),s=e.X(0,[447,145,658],()=>i(1151));module.exports=s})();