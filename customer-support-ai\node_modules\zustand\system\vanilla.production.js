System.register([],function(a){"use strict";return{execute:function(){const o=e=>{let t;const s=new Set,i=(c,l)=>{const n=typeof c=="function"?c(t):c;if(!Object.is(n,t)){const b=t;t=(l!=null?l:typeof n!="object"||n===null)?n:Object.assign({},t,n),s.forEach(d=>d(t,b))}},r=()=>t,u={setState:i,getState:r,getInitialState:()=>S,subscribe:c=>(s.add(c),()=>s.delete(c)),destroy:()=>{s.clear()}},S=t=e(i,r,u);return u},f=a("createStore",e=>e?o(e):o);var g=a("default",e=>f(e))}}});
