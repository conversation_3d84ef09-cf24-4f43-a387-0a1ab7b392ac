(()=>{var e={};e.id=817,e.ids=[817],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6104:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>u,POST:()=>p});var a=t(6559),o=t(8088),n=t(7719),i=t(2190),c=t(6621);async function p(e){try{let r=await e.json(),{data:t,error:s}=await c.N.from("support_cases").insert([{arabic_description:r.arabic_description,english_translation:r.english_translation,department:r.department,case_type:r.case_type,category:r.category,sub_category:r.sub_category,sub_sub_category:r.sub_sub_category,description:r.description,sla:r.sla,notes:r.notes,status:"pending",priority:"medium"}]).select();if(s)throw s;return i.NextResponse.json(t[0])}catch(e){return console.error("Error saving case:",e),i.NextResponse.json({error:"Failed to save case"},{status:500})}}async function u(e){try{let{searchParams:r}=new URL(e.url),t=parseInt(r.get("limit")||"50"),s=parseInt(r.get("offset")||"0"),{data:a,error:o}=await c.N.from("support_cases").select("*").order("created_at",{ascending:!1}).range(s,s+t-1);if(o)throw o;return i.NextResponse.json(a)}catch(e){return console.error("Error fetching cases:",e),i.NextResponse.json({error:"Failed to fetch cases"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/cases/route",pathname:"/api/cases",filename:"route",bundlePath:"app/api/cases/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\senario casses\\customer-support-ai\\src\\app\\api\\cases\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:x}=d;function m(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},6487:()=>{},6621:(e,r,t)=>{"use strict";t.d(r,{N:()=>s});let s={from:e=>({select:()=>({data:[],error:null}),insert:()=>({data:[],error:null})})}},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(6104));module.exports=s})();