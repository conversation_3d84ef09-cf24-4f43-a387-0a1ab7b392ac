{"version": 3, "file": "ColumnOrdering.js", "sources": ["../../../src/features/ColumnOrdering.ts"], "sourcesContent": ["import { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nimport {\n  Column,\n  OnChangeFn,\n  RowData,\n  Table,\n  TableFeature,\n  Updater,\n} from '../types'\n\nimport { orderColumns } from './ColumnGrouping'\nimport { ColumnPinningPosition, _getVisibleLeafColumns } from '..'\n\nexport interface ColumnOrderTableState {\n  columnOrder: ColumnOrderState\n}\n\nexport type ColumnOrderState = string[]\n\nexport interface ColumnOrderOptions {\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnOrder` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#oncolumnorderchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  onColumnOrderChange?: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderColumn {\n  /**\n   * Returns the index of the column in the order of the visible columns. Optionally pass a `position` parameter to get the index of the column in a sub-section of the table\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIndex: (position?: ColumnPinningPosition | 'center') => number\n  /**\n   * Returns `true` if the column is the first column in the order of the visible columns. Optionally pass a `position` parameter to check if the column is the first in a sub-section of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getisfirstcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIsFirstColumn: (position?: ColumnPinningPosition | 'center') => boolean\n  /**\n   * Returns `true` if the column is the last column in the order of the visible columns. Optionally pass a `position` parameter to check if the column is the last in a sub-section of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#getislastcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  getIsLastColumn: (position?: ColumnPinningPosition | 'center') => boolean\n}\n\nexport interface ColumnOrderDefaultOptions {\n  onColumnOrderChange: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderInstance<TData extends RowData> {\n  _getOrderColumnsFn: () => (\n    columns: Column<TData, unknown>[]\n  ) => Column<TData, unknown>[]\n  /**\n   * Resets the **columnOrder** state to `initialState.columnOrder`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#resetcolumnorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  resetColumnOrder: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnOrder` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#setcolumnorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  setColumnOrder: (updater: Updater<ColumnOrderState>) => void\n}\n\n//\n\nexport const ColumnOrdering: TableFeature = {\n  getInitialState: (state): ColumnOrderTableState => {\n    return {\n      columnOrder: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnOrderDefaultOptions => {\n    return {\n      onColumnOrderChange: makeStateUpdater('columnOrder', table),\n    }\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getIndex = memo(\n      position => [_getVisibleLeafColumns(table, position)],\n      columns => columns.findIndex(d => d.id === column.id),\n      getMemoOptions(table.options, 'debugColumns', 'getIndex')\n    )\n    column.getIsFirstColumn = position => {\n      const columns = _getVisibleLeafColumns(table, position)\n      return columns[0]?.id === column.id\n    }\n    column.getIsLastColumn = position => {\n      const columns = _getVisibleLeafColumns(table, position)\n      return columns[columns.length - 1]?.id === column.id\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnOrder = updater =>\n      table.options.onColumnOrderChange?.(updater)\n    table.resetColumnOrder = defaultState => {\n      table.setColumnOrder(\n        defaultState ? [] : table.initialState.columnOrder ?? []\n      )\n    }\n    table._getOrderColumnsFn = memo(\n      () => [\n        table.getState().columnOrder,\n        table.getState().grouping,\n        table.options.groupedColumnMode,\n      ],\n      (columnOrder, grouping, groupedColumnMode) =>\n        (columns: Column<TData, unknown>[]) => {\n          // Sort grouped columns to the start of the column list\n          // before the headers are built\n          let orderedColumns: Column<TData, unknown>[] = []\n\n          // If there is no order, return the normal columns\n          if (!columnOrder?.length) {\n            orderedColumns = columns\n          } else {\n            const columnOrderCopy = [...columnOrder]\n\n            // If there is an order, make a copy of the columns\n            const columnsCopy = [...columns]\n\n            // And make a new ordered array of the columns\n\n            // Loop over the columns and place them in order into the new array\n            while (columnsCopy.length && columnOrderCopy.length) {\n              const targetColumnId = columnOrderCopy.shift()\n              const foundIndex = columnsCopy.findIndex(\n                d => d.id === targetColumnId\n              )\n              if (foundIndex > -1) {\n                orderedColumns.push(columnsCopy.splice(foundIndex, 1)[0]!)\n              }\n            }\n\n            // If there are any columns left, add them to the end\n            orderedColumns = [...orderedColumns, ...columnsCopy]\n          }\n\n          return orderColumns(orderedColumns, grouping, groupedColumnMode)\n        },\n      getMemoOptions(table.options, 'debugTable', '_getOrderColumnsFn')\n    )\n  },\n}\n"], "names": ["ColumnOrdering", "getInitialState", "state", "columnOrder", "getDefaultOptions", "table", "onColumnOrderChange", "makeStateUpdater", "createColumn", "column", "getIndex", "memo", "position", "_getVisibleLeafColumns", "columns", "findIndex", "d", "id", "getMemoOptions", "options", "getIsFirstColumn", "_columns$", "getIsLastColumn", "_columns", "length", "createTable", "setColumnOrder", "updater", "resetColumnOrder", "defaultState", "_table$initialState$c", "initialState", "_getOrderColumnsFn", "getState", "grouping", "groupedColumnMode", "orderedColumns", "columnOrderCopy", "columnsCopy", "targetColumnId", "shift", "foundIndex", "push", "splice", "orderColumns"], "mappings": ";;;;;;;;;;;;;;;;AAwEA;;AAEO,MAAMA,cAA4B,GAAG;EAC1CC,eAAe,EAAGC,KAAK,IAA4B;IACjD,OAAO;AACLC,MAAAA,WAAW,EAAE,EAAE;MACf,GAAGD,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfC,KAAmB,IACW;IAC9B,OAAO;AACLC,MAAAA,mBAAmB,EAAEC,sBAAgB,CAAC,aAAa,EAAEF,KAAK,CAAA;KAC3D,CAAA;GACF;AAEDG,EAAAA,YAAY,EAAEA,CACZC,MAA8B,EAC9BJ,KAAmB,KACV;AACTI,IAAAA,MAAM,CAACC,QAAQ,GAAGC,UAAI,CACpBC,QAAQ,IAAI,CAACC,uCAAsB,CAACR,KAAK,EAAEO,QAAQ,CAAC,CAAC,EACrDE,OAAO,IAAIA,OAAO,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKR,MAAM,CAACQ,EAAE,CAAC,EACrDC,oBAAc,CAACb,KAAK,CAACc,OAAO,EAAE,cAAc,EAAE,UAAU,CAC1D,CAAC,CAAA;AACDV,IAAAA,MAAM,CAACW,gBAAgB,GAAGR,QAAQ,IAAI;AAAA,MAAA,IAAAS,SAAA,CAAA;AACpC,MAAA,MAAMP,OAAO,GAAGD,uCAAsB,CAACR,KAAK,EAAEO,QAAQ,CAAC,CAAA;AACvD,MAAA,OAAO,CAAAS,CAAAA,SAAA,GAAAP,OAAO,CAAC,CAAC,CAAC,KAAVO,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,SAAA,CAAYJ,EAAE,MAAKR,MAAM,CAACQ,EAAE,CAAA;KACpC,CAAA;AACDR,IAAAA,MAAM,CAACa,eAAe,GAAGV,QAAQ,IAAI;AAAA,MAAA,IAAAW,QAAA,CAAA;AACnC,MAAA,MAAMT,OAAO,GAAGD,uCAAsB,CAACR,KAAK,EAAEO,QAAQ,CAAC,CAAA;AACvD,MAAA,OAAO,EAAAW,QAAA,GAAAT,OAAO,CAACA,OAAO,CAACU,MAAM,GAAG,CAAC,CAAC,qBAA3BD,QAAA,CAA6BN,EAAE,MAAKR,MAAM,CAACQ,EAAE,CAAA;KACrD,CAAA;GACF;EAEDQ,WAAW,EAA0BpB,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAACqB,cAAc,GAAGC,OAAO,IAC5BtB,KAAK,CAACc,OAAO,CAACb,mBAAmB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAjCD,KAAK,CAACc,OAAO,CAACb,mBAAmB,CAAGqB,OAAO,CAAC,CAAA;AAC9CtB,IAAAA,KAAK,CAACuB,gBAAgB,GAAGC,YAAY,IAAI;AAAA,MAAA,IAAAC,qBAAA,CAAA;AACvCzB,MAAAA,KAAK,CAACqB,cAAc,CAClBG,YAAY,GAAG,EAAE,IAAAC,qBAAA,GAAGzB,KAAK,CAAC0B,YAAY,CAAC5B,WAAW,YAAA2B,qBAAA,GAAI,EACxD,CAAC,CAAA;KACF,CAAA;AACDzB,IAAAA,KAAK,CAAC2B,kBAAkB,GAAGrB,UAAI,CAC7B,MAAM,CACJN,KAAK,CAAC4B,QAAQ,EAAE,CAAC9B,WAAW,EAC5BE,KAAK,CAAC4B,QAAQ,EAAE,CAACC,QAAQ,EACzB7B,KAAK,CAACc,OAAO,CAACgB,iBAAiB,CAChC,EACD,CAAChC,WAAW,EAAE+B,QAAQ,EAAEC,iBAAiB,KACtCrB,OAAiC,IAAK;AACrC;AACA;MACA,IAAIsB,cAAwC,GAAG,EAAE,CAAA;;AAEjD;AACA,MAAA,IAAI,EAACjC,WAAW,IAAA,IAAA,IAAXA,WAAW,CAAEqB,MAAM,CAAE,EAAA;AACxBY,QAAAA,cAAc,GAAGtB,OAAO,CAAA;AAC1B,OAAC,MAAM;AACL,QAAA,MAAMuB,eAAe,GAAG,CAAC,GAAGlC,WAAW,CAAC,CAAA;;AAExC;AACA,QAAA,MAAMmC,WAAW,GAAG,CAAC,GAAGxB,OAAO,CAAC,CAAA;;AAEhC;;AAEA;AACA,QAAA,OAAOwB,WAAW,CAACd,MAAM,IAAIa,eAAe,CAACb,MAAM,EAAE;AACnD,UAAA,MAAMe,cAAc,GAAGF,eAAe,CAACG,KAAK,EAAE,CAAA;AAC9C,UAAA,MAAMC,UAAU,GAAGH,WAAW,CAACvB,SAAS,CACtCC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKsB,cAChB,CAAC,CAAA;AACD,UAAA,IAAIE,UAAU,GAAG,CAAC,CAAC,EAAE;AACnBL,YAAAA,cAAc,CAACM,IAAI,CAACJ,WAAW,CAACK,MAAM,CAACF,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC,CAAA;AAC5D,WAAA;AACF,SAAA;;AAEA;AACAL,QAAAA,cAAc,GAAG,CAAC,GAAGA,cAAc,EAAE,GAAGE,WAAW,CAAC,CAAA;AACtD,OAAA;AAEA,MAAA,OAAOM,2BAAY,CAACR,cAAc,EAAEF,QAAQ,EAAEC,iBAAiB,CAAC,CAAA;KACjE,EACHjB,oBAAc,CAACb,KAAK,CAACc,OAAO,EAAE,YAAY,EAAE,oBAAoB,CAClE,CAAC,CAAA;AACH,GAAA;AACF;;;;"}