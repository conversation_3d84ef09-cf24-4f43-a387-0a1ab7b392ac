# 🚀 تعليمات إعداد نظام إدارة الحالات

## 📋 الخطوات المطلوبة منك:

### 1️⃣ **إعداد قاعدة البيانات في Supabase**

#### أ) الدخول إلى Supabase:
```
1. اذه<PERSON> إلى: https://supabase.com
2. سجل دخول إلى حسابك
3. اختر مشروعك: bbigwqwtmhctqrptkuni
```

#### ب) إنشاء الجداول:
```
1. اذهب إلى SQL Editor في Supabase
2. انسخ محتوى ملف supabase-schema.sql
3. الصق الكود واضغط Run
4. تأكد من إنشاء الجداول بنجاح
```

#### ج) الحصول على بيانات الاتصال:
```
1. اذهب إلى Settings > API
2. انسخ:
   - Project URL: https://bbigwqwtmhctqrptkuni.supabase.co
   - anon/public key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 2️⃣ **تشغيل البرنامج**

#### أ) فتح البرنامج:
```
1. افتح ملف: advanced-demo.html
2. أدخل بيانات Supabase في الحقول المخصصة
3. اختياري: أدخل OpenAI API Key
```

#### ب) اختبار الاتصال:
```
1. اكتب مشكلة بالعربية
2. ستظهر اقتراحات ذكية (إذا كان هناك بيانات)
3. اضغط "تحليل المشكلة"
4. اضغط "حفظ كحالة جديدة"
```

### 3️⃣ **إضافة بيانات تجريبية (اختياري)**

```sql
-- أضف هذا في SQL Editor لإنشاء بيانات تجريبية
INSERT INTO support_cases (arabic_description, english_translation, department, case_type, category, sub_category, description, sla, notes, confidence_score) VALUES
('العميل بيشتكي إن الحساب بتاعه اتمسح', 'Customer complains that their account was deleted', 'IT', 'Account Problem', 'Access', 'Account Deleted', 'Customer reports account deletion', '4 hours', 'Urgent - check backup', 85),
('الموظف مش قادر يدخل على النظام', 'Employee cannot access the system', 'IT', 'Technical Issue', 'Login', 'Password Issue', 'Employee login problem', '2 hours', 'Reset password', 90),
('العميل دفع بس المبلغ مش ظاهر', 'Customer paid but amount not showing', 'Finance', 'Payment Issue', 'Transaction', 'Payment Not Reflected', 'Payment processing issue', '24 hours', 'Check payment gateway', 88);
```

## 🔧 استكشاف الأخطاء:

### ❌ **إذا لم يعمل الاتصال:**
```
1. تأكد من صحة Supabase URL و Key
2. تأكد من إنشاء الجداول
3. تحقق من Console في المتصفح (F12)
4. تأكد من تفعيل RLS policies
```

### ❌ **إذا لم تظهر الاقتراحات:**
```
1. تأكد من وجود بيانات في الجداول
2. أضف البيانات التجريبية أعلاه
3. جرب البحث اليدوي أولاً
```

### ❌ **إذا لم يحفظ الحالات:**
```
1. تأكد من صحة بيانات Supabase
2. تحقق من permissions في Supabase
3. راجع Console للأخطاء
```

## 🎯 **المميزات المتاحة:**

### ✅ **بدون قاعدة بيانات:**
- تحليل المشاكل بالذكاء الاصطناعي
- عرض النتائج في جدول احترافي
- نسخ وحفظ النتائج
- اقتراحات وهمية للتجربة

### ✅ **مع قاعدة البيانات:**
- حفظ الحالات في Supabase
- بحث في الحالات المشابهة
- اقتراحات ذكية حقيقية
- تتبع جميع الحالات
- إحصائيات ومقاييس

## 📞 **إذا احتجت مساعدة:**

```
1. تأكد من اتباع الخطوات بالترتيب
2. راجع رسائل الخطأ في Console
3. تأكد من صحة بيانات Supabase
4. جرب البيانات التجريبية أولاً
```

## 🚀 **النتيجة المتوقعة:**

بعد إتمام هذه الخطوات ستحصل على:
- ✅ نظام إدارة حالات متكامل
- ✅ بحث ذكي في الحالات المشابهة
- ✅ اقتراحات تلقائية أثناء الكتابة
- ✅ حفظ وتتبع جميع الحالات
- ✅ واجهة احترافية للموظفين
- ✅ تحليل ذكي بالذكاء الاصطناعي
