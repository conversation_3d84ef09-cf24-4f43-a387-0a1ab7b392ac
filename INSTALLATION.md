# 🛠️ دليل التثبيت والإعداد | Installation Guide

## 🎯 خيارات التشغيل

### الخيار الأول: HTML مباشرة (الأسهل) ⭐
- ✅ لا يحتاج تثبيت
- ✅ يعمل على أي جهاز
- ✅ جاهز للاستخدام فوراً
- ❌ مميزات محدودة

### الخيار الثاني: Next.js كامل (الأقوى) 🚀
- ✅ جميع المميزات
- ✅ قاعدة بيانات
- ✅ واجهة احترافية
- ❌ يحتاج إعداد تقني

## 🚀 الطريقة الأولى: HTML مباشرة

### المتطلبات
- متصفح حديث (Chrome, Firefox, Edge, Safari)
- اتصال بالإنترنت (للتصميم والـ API)

### خطوات التشغيل
1. **تحميل الملفات**
   ```
   ✅ simple-demo.html (النسخة التجريبية)
   ✅ advanced-demo.html (النسخة المتقدمة)
   ```

2. **فتح الملف**
   - انقر مرتين على الملف
   - أو اسحبه إلى المتصفح
   - أو انقر بالزر الأيمن > فتح بواسطة > المتصفح

3. **البدء في الاستخدام**
   - أدخل النص العربي
   - اضغط "تحليل المشكلة"
   - احصل على النتيجة!

### إعداد OpenAI API (للنسخة المتقدمة)
1. **إنشاء حساب OpenAI**
   - اذهب إلى: https://platform.openai.com
   - أنشئ حساب جديد
   - تأكد من وجود رصيد ($5 على الأقل)

2. **الحصول على مفتاح API**
   - اذهب إلى: https://platform.openai.com/api-keys
   - اضغط "Create new secret key"
   - انسخ المفتاح (sk-...)
   - احتفظ به في مكان آمن

3. **استخدام المفتاح**
   - افتح `advanced-demo.html`
   - ألصق المفتاح في المربع الأول
   - ابدأ التحليل!

## 🏗️ الطريقة الثانية: Next.js كامل

### المتطلبات التقنية
```bash
Node.js 18+ 
npm أو yarn
Git (اختياري)
```

### خطوة 1: تحضير البيئة
```bash
# تحقق من إصدار Node.js
node --version
# يجب أن يكون 18 أو أحدث

# تحقق من npm
npm --version
```

### خطوة 2: تحميل المشروع
```bash
# إذا كان لديك Git
git clone [repository-url]
cd customer-support-ai

# أو حمل الملفات مباشرة وفك الضغط
```

### خطوة 3: تثبيت المكتبات
```bash
cd customer-support-ai
npm install

# إذا واجهت مشاكل، جرب:
npm install --legacy-peer-deps
```

### خطوة 4: إعداد متغيرات البيئة
1. **إنشاء ملف البيئة**
   ```bash
   # انسخ الملف النموذجي
   cp .env.example .env.local
   
   # أو أنشئ ملف جديد
   touch .env.local
   ```

2. **إضافة المتغيرات**
   ```env
   # OpenAI Configuration
   OPENAI_API_KEY=sk-your-openai-api-key-here
   
   # Supabase Configuration (اختياري)
   NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
   ```

### خطوة 5: تشغيل التطبيق
```bash
# تشغيل وضع التطوير
npm run dev

# أو تشغيل وضع الإنتاج
npm run build
npm start
```

### خطوة 6: فتح التطبيق
- افتح المتصفح
- اذهب إلى: http://localhost:3000
- ابدأ الاستخدام!

## 🗄️ إعداد قاعدة البيانات (اختياري)

### إنشاء مشروع Supabase
1. **التسجيل**
   - اذهب إلى: https://supabase.com
   - أنشئ حساب جديد
   - أنشئ مشروع جديد

2. **الحصول على المفاتيح**
   - اذهب إلى Settings > API
   - انسخ URL و API Keys
   - أضفها لملف `.env.local`

3. **إنشاء الجداول**
   - اذهب إلى SQL Editor في Supabase
   - انسخ محتوى ملف `supabase-schema.sql`
   - نفذ الكود
   - تأكد من إنشاء الجداول بنجاح

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ: "Module not found"
```bash
# احذف node_modules وأعد التثبيت
rm -rf node_modules package-lock.json
npm install
```

#### خطأ: "Port 3000 is already in use"
```bash
# استخدم منفذ مختلف
npm run dev -- -p 3001
```

#### خطأ: "OpenAI API key invalid"
```bash
# تأكد من:
1. صحة المفتاح
2. وجود رصيد في الحساب
3. عدم انتهاء صلاحية المفتاح
```

#### خطأ: "Supabase connection failed"
```bash
# تأكد من:
1. صحة URL والمفاتيح
2. تفعيل Row Level Security
3. إنشاء الجداول المطلوبة
```

### نصائح الأداء
1. **استخدم متصفح حديث** للحصول على أفضل أداء
2. **تأكد من سرعة الإنترنت** للـ API calls
3. **أغلق التطبيقات الأخرى** إذا كان الجهاز بطيء
4. **استخدم النسخة البسيطة** للاختبار السريع

## 📱 التشغيل على أجهزة مختلفة

### Windows
```bash
# استخدم PowerShell أو Command Prompt
npm run dev
```

### macOS
```bash
# استخدم Terminal
npm run dev
```

### Linux
```bash
# استخدم Terminal
npm run dev
```

### الهاتف المحمول
- افتح `simple-demo.html` في متصفح الهاتف
- أو اذهب إلى رابط التطبيق المنشور

## 🚀 النشر والإنتاج

### Vercel (الأسهل)
```bash
# ثبت Vercel CLI
npm i -g vercel

# انشر التطبيق
vercel

# اتبع التعليمات
```

### Netlify
```bash
# بناء التطبيق
npm run build

# ارفع مجلد out/ إلى Netlify
```

### خادم خاص
```bash
# بناء التطبيق
npm run build

# نسخ الملفات إلى الخادم
# تشغيل npm start
```

---

**🎉 تهانينا! النظام جاهز للعمل**

**💡 نصيحة:** ابدأ بالنسخة البسيطة للتجربة، ثم انتقل للنسخة المتقدمة عند الحاجة
