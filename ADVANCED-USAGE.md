# 🎓 دليل الاستخدام المتقدم | Advanced Usage Guide

## 🎯 تحسين جودة النتائج

### كتابة النص الأمثل
```
❌ نص ضعيف: "مشكلة"
✅ نص جيد: "العميل عنده مشكلة في الدفع"
⭐ نص ممتاز: "العميل دفع 500 ريال بالبطاقة الائتمانية بس المبلغ اتخصم من حسابه ومش ظاهر في نظامنا"
```

### عناصر النص المثالي
1. **من؟** - العميل، الموظف، المستخدم
2. **ماذا؟** - طبيعة المشكلة
3. **متى؟** - وقت حدوث المشكلة (إن أمكن)
4. **كيف؟** - الخطوات التي أدت للمشكلة
5. **التأثير؟** - ما هو تأثير المشكلة

### أمثلة متدرجة

#### المستوى الأساسي
```
"العميل مش قادر يدخل"
```
**النتيجة:** تصنيف عام، ثقة متوسطة

#### المستوى المتوسط
```
"العميل مش قادر يدخل على حسابه في الموقع"
```
**النتيجة:** تصنيف أدق، ثقة جيدة

#### المستوى المتقدم
```
"العميل أحمد محمد مش قادر يدخل على حسابه في الموقع من امبارح، بيقول كلمة المرور صحيحة بس النظام بيقوله خطأ، وده بيأثر على شغله لأنه محتاج يراجع الفواتير"
```
**النتيجة:** تصنيف دقيق جداً، ثقة عالية

## 🔍 فهم مخرجات النظام

### تفسير الأعمدة

#### Department (القسم)
- **IT**: مشاكل تقنية، أنظمة، برمجيات
- **Finance**: مدفوعات، فواتير، حسابات
- **HR**: موظفين، رواتب، إجازات
- **Logistics**: شحن، توصيل، مخازن
- **Customer Service**: استفسارات عامة، شكاوى
- **Sales**: مبيعات، عروض، منتجات

#### Case Type (نوع الحالة)
- **Technical Issue**: مشاكل تقنية
- **Account Problem**: مشاكل الحسابات
- **Payment Issue**: مشاكل الدفع
- **Shipping Problem**: مشاكل الشحن
- **General Inquiry**: استفسارات عامة

#### SLA (وقت الاستجابة)
- **1 hour**: حرج جداً
- **4 hours**: مهم
- **24 hours**: عادي
- **48 hours**: غير عاجل

### مستويات الثقة
- **90-100%**: ثقة عالية جداً - النتيجة دقيقة
- **80-89%**: ثقة عالية - النتيجة موثوقة
- **70-79%**: ثقة متوسطة - قد تحتاج مراجعة
- **60-69%**: ثقة منخفضة - تحتاج تأكيد
- **أقل من 60%**: ثقة ضعيفة - تحتاج إعادة كتابة

## 🎛️ تخصيص النظام

### تعديل معايير التصنيف

#### في النسخة HTML
```javascript
// في ملف advanced-demo.html
// ابحث عن دالة getMockAnalysis
// عدّل الشروط حسب احتياجاتك

if (text.includes('كلمات مخصصة')) {
    return {
        department: 'القسم المخصص',
        case_type: 'نوع مخصص',
        // ... باقي الحقول
    };
}
```

#### في مشروع Next.js
```javascript
// في ملف src/lib/openai.ts
// عدّل system prompt لإضافة معايير جديدة

const systemPrompt = `
أضف هنا معايير التصنيف الخاصة بشركتك:
- قسم جديد: الوصف
- نوع حالة جديد: الوصف
`;
```

### إضافة أقسام جديدة
```javascript
// أضف في قائمة الأقسام
const departments = [
    'IT', 'Finance', 'HR', 'Logistics', 
    'Customer Service', 'Sales',
    'القسم الجديد', 'قسم آخر'  // أقسامك الجديدة
];
```

## 📊 تحليل البيانات المتقدم

### تتبع الأنماط
```
مشاكل شائعة هذا الأسبوع:
- مشاكل تسجيل الدخول: 45%
- مشاكل الدفع: 30%
- مشاكل الشحن: 25%
```

### تحليل الأولويات
```
حالات حرجة (1 hour SLA): 15%
حالات مهمة (4 hours SLA): 35%
حالات عادية (24+ hours SLA): 50%
```

### تقييم الأداء
```
متوسط وقت التصنيف:
- قبل النظام: 5-10 دقائق
- بعد النظام: 10 ثوان
- تحسن: 95%
```

## 🔧 التكامل مع الأنظمة الأخرى

### تصدير البيانات
```javascript
// تصدير كـ JSON
const exportData = {
    timestamp: new Date().toISOString(),
    original_text: arabicText,
    analysis: result,
    confidence: result.confidence_score
};

// حفظ في ملف
const dataStr = JSON.stringify(exportData, null, 2);
const dataBlob = new Blob([dataStr], {type: 'application/json'});
```

### تصدير كـ CSV
```javascript
// تحويل لـ CSV
const csvData = [
    ['Department', 'Case Type', 'Category', 'SLA', 'Confidence'],
    [result.department, result.case_type, result.category, result.sla, result.confidence_score]
];

const csvContent = csvData.map(row => row.join(',')).join('\n');
```

### ربط مع CRM
```javascript
// مثال للربط مع نظام CRM
const crmData = {
    ticket_id: generateTicketId(),
    customer_id: extractCustomerId(arabicText),
    department: result.department,
    priority: calculatePriority(result.sla),
    description: result.description,
    created_at: new Date().toISOString()
};

// إرسال لـ CRM API
await sendToCRM(crmData);
```

## 🎯 حالات استخدام متخصصة

### 1. مراكز الاتصال الكبيرة
```javascript
// معالجة دفعية للمكالمات
const batchProcess = async (calls) => {
    const results = [];
    for (const call of calls) {
        const analysis = await analyzeCall(call.transcript);
        results.push({
            call_id: call.id,
            analysis: analysis,
            agent_id: call.agent_id
        });
    }
    return results;
};
```

### 2. تحليل البريد الإلكتروني
```javascript
// استخراج النص من البريد
const extractEmailContent = (email) => {
    // إزالة التوقيع والهيدر
    const cleanText = email.body
        .replace(/^From:.*$/gm, '')
        .replace(/^Subject:.*$/gm, '')
        .replace(/--.*$/gm, '');
    
    return cleanText.trim();
};
```

### 3. تحليل وسائل التواصل الاجتماعي
```javascript
// تحليل تغريدات أو منشورات
const analyzeSocialMedia = async (posts) => {
    const analyses = [];
    for (const post of posts) {
        if (post.language === 'ar') {
            const analysis = await analyzeCustomerIssue({
                arabicText: post.content
            });
            analyses.push({
                platform: post.platform,
                user: post.user,
                analysis: analysis
            });
        }
    }
    return analyses;
};
```

## 📈 تحسين الأداء

### تحسين سرعة الاستجابة
```javascript
// تخزين مؤقت للنتائج المتشابهة
const cache = new Map();

const analyzeWithCache = async (text) => {
    const hash = generateHash(text);
    if (cache.has(hash)) {
        return cache.get(hash);
    }
    
    const result = await analyzeCustomerIssue({arabicText: text});
    cache.set(hash, result);
    return result;
};
```

### تحسين دقة النتائج
```javascript
// تجميع نتائج متعددة للحصول على دقة أعلى
const analyzeWithConsensus = async (text) => {
    const results = await Promise.all([
        analyzeCustomerIssue({arabicText: text}),
        analyzeCustomerIssue({arabicText: text}),
        analyzeCustomerIssue({arabicText: text})
    ]);
    
    // اختيار النتيجة الأكثر تكراراً
    return getMostCommonResult(results);
};
```

## 🔒 الأمان والخصوصية

### حماية البيانات الحساسة
```javascript
// إزالة المعلومات الشخصية قبل التحليل
const sanitizeText = (text) => {
    return text
        .replace(/\d{10,}/g, '[رقم هاتف]')
        .replace(/\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b/g, '[رقم بطاقة]')
        .replace(/[\w\.-]+@[\w\.-]+\.\w+/g, '[بريد إلكتروني]');
};
```

### تشفير البيانات المحفوظة
```javascript
// تشفير النتائج قبل الحفظ
const encryptResult = (result, key) => {
    const encrypted = encrypt(JSON.stringify(result), key);
    return encrypted;
};
```

---

**🎓 تهانينا! أنت الآن خبير في استخدام النظام**

**💡 نصيحة:** ابدأ بالاستخدام البسيط ثم تدرج للمميزات المتقدمة حسب احتياجاتك
